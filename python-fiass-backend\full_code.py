import os
import csv
import io
import re
import json
import pandas as pd
import numpy as np
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
import faiss
from langchain_huggingface.embeddings import HuggingFaceEmbeddings
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import uuid
import time
import database  # Import our database module
from datetime import datetime
import requests
from openai import OpenAI
import openpyxl  # For Excel file processing
import hashlib  # For cache key generation

# Response formatting utilities
class ResponseFormatter:
    """
    Utility class for standardizing API response formats across the application.
    Provides consistent structure, error handling, and user-friendly messaging.
    """

    @staticmethod
    def success_response(data: Any = None, message: str = "Operation completed successfully",
                        metadata: Dict[str, Any] = None, status_code: int = 200) -> tuple:
        """
        Create a standardized success response.

        Args:
            data: The main response data
            message: Success message for the user
            metadata: Additional context information
            status_code: HTTP status code

        Returns:
            tuple: (response_dict, status_code)
        """
        response = {
            "success": True,
            "status": "success",
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }

        if metadata:
            response["metadata"] = metadata

        return response, status_code

    @staticmethod
    def error_response(error: str, error_type: str = "general_error",
                      details: Dict[str, Any] = None, status_code: int = 400) -> tuple:
        """
        Create a standardized error response with user-friendly messaging.

        Args:
            error: Error message
            error_type: Category of error for better handling
            details: Additional error context
            status_code: HTTP status code

        Returns:
            tuple: (response_dict, status_code)
        """
        # Map technical errors to user-friendly messages
        user_friendly_messages = {
            "csv_parse_error": "Unable to process the CSV file. Please check the file format and try again.",
            "index_creation_error": "Failed to create the search index. Please try again or contact support.",
            "faiss_access_error": "Unable to access the search database. Please try again later.",
            "upload_cancelled": "The upload process was cancelled by the user.",
            "network_error": "Network connection issue. Please check your connection and try again.",
            "validation_error": "Invalid input provided. Please check your data and try again.",
            "permission_error": "You don't have permission to perform this action.",
            "not_found_error": "The requested resource was not found.",
            "rate_limit_error": "Too many requests. Please wait a moment and try again."
        }

        user_message = user_friendly_messages.get(error_type, error)

        response = {
            "success": False,
            "status": "error",
            "error": {
                "message": user_message,
                "type": error_type,
                "technical_details": error if error_type in user_friendly_messages else None
            },
            "timestamp": datetime.now().isoformat()
        }

        if details:
            response["error"]["details"] = details

        return response, status_code

    @staticmethod
    def processing_response(message: str, progress: Dict[str, Any] = None,
                          process_id: str = None) -> Dict[str, Any]:
        """
        Create a response for ongoing processing operations.

        Args:
            message: Status message
            progress: Progress information
            process_id: Unique identifier for the process

        Returns:
            dict: Response dictionary
        """
        response = {
            "success": True,
            "status": "processing",
            "message": message,
            "timestamp": datetime.now().isoformat()
        }

        if progress:
            response["progress"] = progress

        if process_id:
            response["process_id"] = process_id

        return response

    @staticmethod
    def data_response(data: Any, total_count: int = None, page_info: Dict[str, Any] = None,
                     message: str = "Data retrieved successfully") -> Dict[str, Any]:
        """
        Create a response for data retrieval operations.

        Args:
            data: The retrieved data
            total_count: Total number of items available
            page_info: Pagination information
            message: Success message

        Returns:
            dict: Response dictionary
        """
        response = {
            "success": True,
            "status": "success",
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }

        if total_count is not None:
            response["total_count"] = total_count

        if page_info:
            response["pagination"] = page_info

        return response

# Load environment variables
load_dotenv()
NEWS_API_KEY = os.getenv("NEWS_API_KEY")

# Configuration
# Get the directory where this script is located
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
FAISS_DATA_DIR = os.getenv("FAISS_DATA_DIR", os.path.join(SCRIPT_DIR, "faiss_data"))  # Directory to store FAISS files
CHUNK_SIZE = int(os.getenv("CHUNK_SIZE", 500))
BATCH_SIZE = int(os.getenv("BATCH_SIZE", 10))

DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
API_ENVIRONMENT = os.getenv("API_ENVIRONMENT", "development")  # Default to development if not set

# Ensure FAISS data directory exists
os.makedirs(FAISS_DATA_DIR, exist_ok=True)

# Available embedding models
EMBEDDING_MODELS = {
    "all-MiniLM-L6-v2": {
        "name": "all-MiniLM-L6-v2",
        "dimension": 384,
        "description": "Sentence Transformers model for general purpose embeddings"
    },
    "all-mpnet-base-v2": {
        "name": "all-mpnet-base-v2",
        "dimension": 768,
        "description": "Higher quality model with larger dimensions"
    },
    "paraphrase-multilingual-MiniLM-L12-v2": {
        "name": "paraphrase-multilingual-MiniLM-L12-v2",
        "dimension": 384,
        "description": "Multilingual model supporting 50+ languages"
    },
    "distiluse-base-multilingual-cased-v1": {
        "name": "distiluse-base-multilingual-cased-v1",
        "dimension": 512,
        "description": "Efficient multilingual model"
    }
}

# Default embedding model
DEFAULT_EMBED_MODEL = "all-MiniLM-L6-v2"

# Dictionary to track active uploads
active_uploads = {}
active_uploads_lock = threading.Lock()

# Dictionary to cache embedding models
embedding_models = {}

# Initialize Flask app
app = Flask(__name__)

# Configure CORS with more permissive settings for development
CORS(app,
     resources={
         r"/api/*": {
             "origins": [
                 "http://localhost:3000",
                 "http://localhost:3001",
                 "http://127.0.0.1:3000",
                 "http://127.0.0.1:3001",
                 "http://**************:3000",
                 "http://**************:3001"
             ],
             "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
             "allow_headers": ["Content-Type", "Authorization", "X-Requested-With", "Origin"],
             "supports_credentials": True
         },
         r"/financial_query": {
             "origins": [
                 "http://localhost:3000",
                 "http://localhost:3001",
                 "http://127.0.0.1:3000",
                 "http://127.0.0.1:3001",
                 "http://**************:3000",
                 "http://**************:3001"
             ],
             "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
             "allow_headers": ["Content-Type", "Authorization", "X-Requested-With", "Origin"],
             "supports_credentials": True
         }
     },
     # Additional CORS settings for development
     send_wildcard=False,
     vary_header=True
)

def get_embedder(model_name=DEFAULT_EMBED_MODEL):
    """
    Get or initialize an embedding model.

    Args:
        model_name: Name of the embedding model to use

    Returns:
        HuggingFaceEmbeddings: The embedding model
    """
    if model_name not in EMBEDDING_MODELS:
        print(f"Warning: Unknown model '{model_name}'. Using default model '{DEFAULT_EMBED_MODEL}'")
        model_name = DEFAULT_EMBED_MODEL

    if model_name not in embedding_models:
        print(f"Initializing embedding model: {model_name}")
        embedding_models[model_name] = HuggingFaceEmbeddings(model_name=model_name)

    return embedding_models[model_name]

# Initialize default embedding model
embedder = get_embedder(DEFAULT_EMBED_MODEL)

def clean_multilingual_response(response_data, language_code):
    """
    Clean and enhance multilingual response data for better UI presentation.
    
    Args:
        response_data: The response data dictionary
        language_code: Language code (te, kn, ta, etc.)
        
    Returns:
        dict: Cleaned and enhanced response data
    """
    if not response_data or not isinstance(response_data, dict):
        return response_data
    
    # Language-specific text cleaning
    text_fields = ['ai_response', 'summary', 'context', 'explanation']
    
    for field in text_fields:
        if field in response_data and response_data[field]:
            text = response_data[field]
            
            # Apply corruption detection and cleaning
            is_corrupted, cleaned_text, corruption_details = detect_text_corruption(text)
            
            if is_corrupted:
                print(f"🔧 Cleaned corruption in {field} for language {language_code}")
                response_data[field] = cleaned_text
                
                # Add cleaning metadata
                if 'cleaning_metadata' not in response_data:
                    response_data['cleaning_metadata'] = {}
                response_data['cleaning_metadata'][f'{field}_cleaned'] = True
                response_data['cleaning_metadata'][f'{field}_corruption_type'] = corruption_details.get('reason', 'unknown')
    
    # Clean related questions using the safer approach for multilingual languages
    if 'related_questions' in response_data and response_data['related_questions']:
        response_data['related_questions'] = clean_related_questions_safe(response_data['related_questions'], language_code)
    
    return response_data

def clean_related_questions_safe(questions, language_code):
    """
    Safely clean related questions for multilingual languages without over-processing.
    
    Args:
        questions: List of related questions
        language_code: Language code (Tamil, Telugu, Kannada, Oriya, etc.)
        
    Returns:
        list: Cleaned questions
    """
    if not questions or not isinstance(questions, list):
        return questions
    
    multilingual_languages = ['Tamil', 'Telugu', 'Kannada', 'Oriya', 'Odia']
    is_multilingual = language_code in multilingual_languages
    
    print(f"🔍 Cleaning {len(questions)} related questions for {language_code} (multilingual: {is_multilingual})")
    
    cleaned_questions = []
    
    for i, question in enumerate(questions):
        if not question or not question.strip():
            cleaned_questions.append(question)
            continue
            
        if is_multilingual:
            # For multilingual languages, be very conservative
            # Only remove obvious word repetitions, preserve character diversity
            word_corrupted, word_cleaned = detect_word_repetition(question)
            if word_corrupted:
                print(f"🔧 Word repetition cleaned in question {i+1}: {question[:30]}... -> {word_cleaned[:30]}...")
                cleaned_questions.append(word_cleaned)
            else:
                # Preserve the question as-is
                cleaned_questions.append(question)
        else:
            # For other languages, apply standard cleaning
            is_corrupted, cleaned_question, _ = detect_text_corruption(question)
            if is_corrupted:
                print(f"🔧 Corruption cleaned in question {i+1}: {question[:30]}... -> {cleaned_question[:30]}...")
                cleaned_questions.append(cleaned_question)
            else:
                cleaned_questions.append(question)
    
    print(f"✅ Related questions cleaning completed: {len(cleaned_questions)} questions preserved")
    return cleaned_questions

def detect_word_repetition(text):
    """
    Detect excessive word repetition patterns that indicate corruption.
    
    Args:
        text: The text to analyze
        
    Returns:
        tuple: (is_corrupted, cleaned_text)
    """
    if len(text.strip()) <= 10:
        return False, text
    
    import re
    
    # Split text into words, preserving punctuation context
    words = re.findall(r'\S+', text)
    
    if len(words) < 3:
        return False, text
    
    # First pass: Remove consecutive duplicates
    consecutive_cleaned = []
    consecutive_removed = 0
    i = 0
    
    while i < len(words):
        current_word = words[i]
        
        # Check for consecutive identical words
        consecutive_count = 1
        j = i + 1
        
        # Count consecutive identical words
        while j < len(words) and words[j].lower() == current_word.lower():
            consecutive_count += 1
            j += 1
        
        # If we found consecutive duplicates, keep only one
        if consecutive_count > 1:
            consecutive_removed += consecutive_count - 1
            consecutive_cleaned.append(current_word)
            print(f"🔧 Removed {consecutive_count - 1} consecutive duplicate(s) of word: '{current_word}'")
            i = j  # Skip all the duplicates
        else:
            consecutive_cleaned.append(current_word)
            i += 1
    
    # Second pass: Remove non-consecutive duplicates if there's still high repetition
    word_freq = {}
    for word in consecutive_cleaned:
        word_lower = word.lower().strip('.,!?')
        word_freq[word_lower] = word_freq.get(word_lower, 0) + 1
    
    # Check if any word appears more than twice (indicating excessive repetition)
    excessive_words = {word: count for word, count in word_freq.items() if count > 2}
    
    final_cleaned = []
    word_usage = {}
    additional_removed = 0
    
    if excessive_words:
        print(f"🔧 Found excessive repetition: {excessive_words}")
        for word in consecutive_cleaned:
            word_lower = word.lower().strip('.,!?')
            current_usage = word_usage.get(word_lower, 0)
            
            if word_lower in excessive_words and current_usage >= 2:
                # Skip this word (already used twice)
                additional_removed += 1
                print(f"🔧 Removed additional duplicate of word: '{word}'")
            else:
                final_cleaned.append(word)
                word_usage[word_lower] = current_usage + 1
    else:
        final_cleaned = consecutive_cleaned
    
    total_removed = consecutive_removed + additional_removed
    repetition_ratio = total_removed / len(words) if len(words) > 0 else 0
    
    print(f"🔍 Word repetition analysis:")
    print(f"   Total words: {len(words)}")
    print(f"   Repeated words found: {total_removed}")
    print(f"   Repetition ratio: {repetition_ratio:.3f}")
    
    # If more than 10% of words are repeated, consider it corrupted
    is_corrupted = repetition_ratio > 0.1
    
    if is_corrupted:
        print(f"🚨 Excessive word repetition detected ({repetition_ratio:.1%})")
    
    # Reconstruct cleaned text
    cleaned_text = ' '.join(final_cleaned)
    
    return is_corrupted, cleaned_text

def calculate_script_aware_char_diversity(text):
    """
    Enhanced script-aware character diversity calculation with better analysis.
    
    Args:
        text: The text to analyze
        
    Returns:
        dict: Comprehensive diversity analysis including:
            - diversity_ratio: Basic unique/total ratio
            - normalized_diversity: Script-aware normalized diversity
            - script_type: Detected script type
            - char_distribution: Character frequency distribution
            - complexity_score: Overall text complexity score
    """
    if not text or len(text.strip()) == 0:
        return {
            'diversity_ratio': 0.0,
            'normalized_diversity': 0.0,
            'script_type': 'unknown',
            'char_distribution': {},
            'complexity_score': 0.0
        }
    
    # Remove excessive whitespace but preserve structure
    clean_text = ' '.join(text.split())
    if len(clean_text) == 0:
        return {
            'diversity_ratio': 0.0,
            'normalized_diversity': 0.0,
            'script_type': 'unknown',
            'char_distribution': {},
            'complexity_score': 0.0
        }
    
    # Basic diversity calculation
    unique_chars = len(set(clean_text))
    total_chars = len(clean_text)
    basic_diversity = unique_chars / total_chars
    
    # Enhanced script detection with character frequency analysis
    script_analysis = analyze_script_composition(clean_text)
    dominant_script = script_analysis['dominant_script']
    
    # Calculate script-specific normalized diversity
    script_expectations = {
        'latin': {'base_diversity': 0.15, 'combining_factor': 1.0},
        'devanagari': {'base_diversity': 0.12, 'combining_factor': 0.8},
        'telugu': {'base_diversity': 0.08, 'combining_factor': 0.7},
        'tamil': {'base_diversity': 0.08, 'combining_factor': 0.7},
        'kannada': {'base_diversity': 0.08, 'combining_factor': 0.7},
        'malayalam': {'base_diversity': 0.08, 'combining_factor': 0.7},
        'bengali': {'base_diversity': 0.10, 'combining_factor': 0.8},
        'gujarati': {'base_diversity': 0.10, 'combining_factor': 0.8},
        'odia': {'base_diversity': 0.08, 'combining_factor': 0.7},
        'arabic': {'base_diversity': 0.10, 'combining_factor': 0.8},
        'chinese': {'base_diversity': 0.06, 'combining_factor': 0.9},
        'japanese': {'base_diversity': 0.07, 'combining_factor': 0.9},
        'korean': {'base_diversity': 0.08, 'combining_factor': 0.8},
        'mixed': {'base_diversity': 0.12, 'combining_factor': 0.9},
        'default': {'base_diversity': 0.12, 'combining_factor': 1.0}
    }
    
    expectations = script_expectations.get(dominant_script, script_expectations['default'])
    
    # Calculate normalized diversity based on script expectations
    expected_diversity = expectations['base_diversity']
    combining_factor = expectations['combining_factor']
    
    # Adjust for text length (longer texts tend to have lower diversity)
    length_factor = min(1.0, 100 / len(clean_text)) if len(clean_text) > 100 else 1.0
    adjusted_expected = expected_diversity * (1 + length_factor * 0.2)
    
    normalized_diversity = (basic_diversity / adjusted_expected) * combining_factor
    
    # Calculate character distribution for pattern analysis
    from collections import Counter
    char_freq = Counter(clean_text)
    char_distribution = {
        'most_common': char_freq.most_common(5),
        'entropy': calculate_text_entropy(clean_text),
        'repetition_score': calculate_repetition_score(clean_text)
    }
    
    # Calculate overall complexity score
    complexity_score = calculate_text_complexity(clean_text, script_analysis, char_distribution)
    
    return {
        'diversity_ratio': basic_diversity,
        'normalized_diversity': normalized_diversity,
        'script_type': dominant_script,
        'script_composition': script_analysis,
        'char_distribution': char_distribution,
        'complexity_score': complexity_score,
        'length_factor': length_factor,
        'expected_diversity': adjusted_expected
    }

def analyze_script_composition(text):
    """
    Analyze the script composition of text with detailed breakdown.
    
    Args:
        text: Text to analyze
        
    Returns:
        dict: Script composition analysis
    """
    script_counts = {
        'latin': 0, 'devanagari': 0, 'telugu': 0, 'tamil': 0,
        'kannada': 0, 'malayalam': 0, 'bengali': 0, 'gujarati': 0,
        'odia': 0, 'arabic': 0, 'chinese': 0, 'japanese': 0, 'korean': 0,
        'punctuation': 0, 'digits': 0, 'whitespace': 0, 'other': 0
    }
    
    for char in text:
        code = ord(char)
        
        # Whitespace
        if char.isspace():
            script_counts['whitespace'] += 1
        # Digits
        elif char.isdigit():
            script_counts['digits'] += 1
        # Punctuation
        elif not char.isalnum():
            script_counts['punctuation'] += 1
        # Latin (including extended)
        elif (0x0041 <= code <= 0x007A) or (0x00C0 <= code <= 0x024F):
            script_counts['latin'] += 1
        # Devanagari
        elif 0x0900 <= code <= 0x097F:
            script_counts['devanagari'] += 1
        # Telugu
        elif 0x0C00 <= code <= 0x0C7F:
            script_counts['telugu'] += 1
        # Tamil
        elif 0x0B80 <= code <= 0x0BFF:
            script_counts['tamil'] += 1
        # Kannada
        elif 0x0C80 <= code <= 0x0CFF:
            script_counts['kannada'] += 1
        # Malayalam
        elif 0x0D00 <= code <= 0x0D7F:
            script_counts['malayalam'] += 1
        # Bengali
        elif 0x0980 <= code <= 0x09FF:
            script_counts['bengali'] += 1
        # Gujarati
        elif 0x0A80 <= code <= 0x0AFF:
            script_counts['gujarati'] += 1
        # Odia (Oriya)
        elif 0x0B00 <= code <= 0x0B7F:
            script_counts['odia'] += 1
        # Arabic
        elif 0x0600 <= code <= 0x06FF:
            script_counts['arabic'] += 1
        # Chinese
        elif 0x4E00 <= code <= 0x9FFF:
            script_counts['chinese'] += 1
        # Japanese (Hiragana, Katakana)
        elif (0x3040 <= code <= 0x309F) or (0x30A0 <= code <= 0x30FF):
            script_counts['japanese'] += 1
        # Korean
        elif 0xAC00 <= code <= 0xD7AF:
            script_counts['korean'] += 1
        else:
            script_counts['other'] += 1
    
    total_chars = len(text)
    script_percentages = {script: (count / total_chars) * 100 
                         for script, count in script_counts.items() if count > 0}
    
    # Determine dominant script (excluding whitespace, punctuation, digits)
    content_scripts = {k: v for k, v in script_counts.items() 
                      if k not in ['whitespace', 'punctuation', 'digits', 'other'] and v > 0}
    
    if not content_scripts:
        dominant_script = 'default'
    elif len(content_scripts) == 1:
        dominant_script = list(content_scripts.keys())[0]
    else:
        # Mixed script - determine primary
        max_script = max(content_scripts, key=content_scripts.get)
        max_percentage = script_percentages.get(max_script, 0)
        
        if max_percentage > 60:
            dominant_script = max_script
        else:
            dominant_script = 'mixed'
    
    return {
        'counts': script_counts,
        'percentages': script_percentages,
        'dominant_script': dominant_script,
        'is_mixed': len(content_scripts) > 1,
        'content_scripts': list(content_scripts.keys())
    }

def calculate_text_entropy(text):
    """Calculate Shannon entropy of text."""
    from collections import Counter
    import math
    
    if not text:
        return 0.0
    
    char_freq = Counter(text)
    text_len = len(text)
    
    entropy = 0.0
    for count in char_freq.values():
        probability = count / text_len
        if probability > 0:
            entropy -= probability * math.log2(probability)
    
    return entropy

def calculate_repetition_score(text):
    """Calculate repetition score based on n-gram analysis."""
    if len(text) < 4:
        return 0.0
    
    # Check for character repetitions
    char_repetitions = 0
    for i in range(len(text) - 1):
        if text[i] == text[i + 1]:
            char_repetitions += 1
    
    # Check for bigram repetitions
    bigrams = [text[i:i+2] for i in range(len(text) - 1)]
    from collections import Counter
    bigram_freq = Counter(bigrams)
    
    repetition_score = 0.0
    for bigram, count in bigram_freq.items():
        if count > 1:
            repetition_score += (count - 1) / len(bigrams)
    
    # Combine character and bigram repetition scores
    char_score = char_repetitions / (len(text) - 1) if len(text) > 1 else 0
    total_score = (char_score + repetition_score) / 2
    
    return min(1.0, total_score)

def calculate_text_complexity(text, script_analysis, char_distribution):
    """Calculate overall text complexity score."""
    if not text:
        return 0.0
    
    # Base complexity from entropy
    entropy_score = char_distribution['entropy'] / 8.0  # Normalize to 0-1
    
    # Script complexity factor
    script_complexity = {
        'latin': 0.7, 'devanagari': 0.9, 'telugu': 1.0, 'tamil': 1.0,
        'kannada': 1.0, 'malayalam': 1.0, 'bengali': 0.9, 'gujarati': 0.9,
        'arabic': 0.8, 'chinese': 1.0, 'japanese': 1.0, 'korean': 0.9,
        'mixed': 0.8, 'default': 0.7
    }
    
    script_factor = script_complexity.get(script_analysis['dominant_script'], 0.7)
    
    # Length complexity (longer texts are generally more complex)
    length_factor = min(1.0, len(text) / 1000)
    
    # Repetition penalty
    repetition_penalty = 1.0 - char_distribution['repetition_score']
    
    # Mixed script bonus
    mixed_bonus = 1.1 if script_analysis['is_mixed'] else 1.0
    
    complexity = (entropy_score * script_factor * length_factor * repetition_penalty * mixed_bonus)
    
    return min(1.0, complexity)

def detect_text_corruption(text, char_diversity_input=None):
    """
    Optimized text corruption detection with special handling for Tamil and other regional languages.
    
    Args:
        text: The text to analyze
        char_diversity_input: Pre-calculated character diversity (for backward compatibility)
        
    Returns:
        tuple: (is_corrupted, cleaned_text, corruption_details)
    """
    if len(text.strip()) <= 10:
        return False, text, {'reason': 'text_too_short', 'confidence': 0.0}
    
    # Check if this is Tamil text - use optimized processing
    import re
    tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
    is_tamil = bool(tamil_pattern.search(text))
    
    if is_tamil:
        # Use conservative Tamil processing
        return detect_tamil_corruption_conservative(text)
    
    # For non-Tamil text, use the original logic but with more lenient thresholds
    word_corrupted, cleaned_text = detect_word_repetition(text)
    
    # If word repetition was found, use the cleaned text for further analysis
    analysis_text = cleaned_text if word_corrupted else text
    
    # Get comprehensive character diversity analysis
    if isinstance(char_diversity_input, dict):
        diversity_analysis = char_diversity_input
    elif isinstance(char_diversity_input, (int, float)):
        diversity_analysis = calculate_script_aware_char_diversity(analysis_text)
    else:
        diversity_analysis = calculate_script_aware_char_diversity(analysis_text)
    
    script_type = diversity_analysis['script_type']
    normalized_diversity = diversity_analysis['normalized_diversity']
    complexity_score = diversity_analysis['complexity_score']
    char_distribution = diversity_analysis['char_distribution']
    
    print(f"🔍 Enhanced corruption analysis:")
    print(f"   Script: {script_type}")
    print(f"   Normalized diversity: {normalized_diversity:.3f}")
    print(f"   Complexity score: {complexity_score:.3f}")
    print(f"   Repetition score: {char_distribution['repetition_score']:.3f}")
    print(f"   Entropy: {char_distribution['entropy']:.3f}")
    
    # Much more lenient thresholds for all regional languages
    script_specific_thresholds = {
        'telugu': {
            'normalized_diversity_min': 0.2,  # Very lenient
            'complexity_min': 0.05,
            'repetition_max': 0.7,
            'entropy_min': 0.8,
            'single_char_dominance_max': 0.6
        },
        'tamil': {
            'normalized_diversity_min': 0.2,  # Very lenient
            'complexity_min': 0.05,
            'repetition_max': 0.7,
            'entropy_min': 0.8,
            'single_char_dominance_max': 0.6
        },
        'kannada': {
            'normalized_diversity_min': 0.2,  # Very lenient
            'complexity_min': 0.05,
            'repetition_max': 0.7,
            'entropy_min': 0.8,
            'single_char_dominance_max': 0.6
        },
        'malayalam': {
            'normalized_diversity_min': 0.2,
            'complexity_min': 0.05,
            'repetition_max': 0.7,
            'entropy_min': 0.8,
            'single_char_dominance_max': 0.6
        },
        'odia': {
            'normalized_diversity_min': 0.2,  # Very lenient
            'complexity_min': 0.05,
            'repetition_max': 0.7,
            'entropy_min': 0.8,
            'single_char_dominance_max': 0.6
        },
        'default': {
            'normalized_diversity_min': 0.5,  # More lenient default
            'complexity_min': 0.15,
            'repetition_max': 0.5,
            'entropy_min': 1.5,
            'single_char_dominance_max': 0.5
        }
    }
    
    # Get script-specific thresholds
    corruption_thresholds = script_specific_thresholds.get(script_type, script_specific_thresholds['default'])
    
    corruption_indicators = []
    corruption_score = 0.0
    
    # Only check for severe corruption indicators
    # Check normalized diversity (only flag if extremely low)
    if normalized_diversity < corruption_thresholds['normalized_diversity_min']:
        indicator_strength = (corruption_thresholds['normalized_diversity_min'] - normalized_diversity) / corruption_thresholds['normalized_diversity_min']
        corruption_indicators.append({
            'type': 'low_normalized_diversity',
            'strength': indicator_strength,
            'value': normalized_diversity,
            'threshold': corruption_thresholds['normalized_diversity_min']
        })
        corruption_score += indicator_strength * 0.2  # Reduced weight
    
    # Check repetition score (only flag if very high)
    if char_distribution['repetition_score'] > corruption_thresholds['repetition_max']:
        indicator_strength = (char_distribution['repetition_score'] - corruption_thresholds['repetition_max']) / (1.0 - corruption_thresholds['repetition_max'])
        corruption_indicators.append({
            'type': 'high_repetition',
            'strength': indicator_strength,
            'value': char_distribution['repetition_score'],
            'threshold': corruption_thresholds['repetition_max']
        })
        corruption_score += indicator_strength * 0.4  # Main indicator
    
    # Special handling for Indian scripts - be even more lenient
    if script_type in ['telugu', 'tamil', 'kannada', 'malayalam', 'devanagari', 'bengali', 'gujarati']:
        corruption_score *= 0.3  # Very aggressive reduction
        print(f"🔍 Applied Indian script adjustment, reduced corruption score to: {corruption_score:.3f}")
    
    # Much higher corruption threshold - only flag obvious corruption
    corruption_threshold = 0.9  # Very high threshold
    
    # Only consider corrupted if we have clear word repetition OR very high corruption score
    is_corrupted = (corruption_score > corruption_threshold) or word_corrupted
    
    # Prepare detailed corruption analysis
    corruption_details = {
        'is_corrupted': is_corrupted,
        'corruption_score': corruption_score,
        'corruption_threshold': corruption_threshold,
        'word_repetition_detected': word_corrupted,
        'indicators': corruption_indicators,
        'diversity_analysis': diversity_analysis,
        'confidence': min(1.0, corruption_score) if is_corrupted else max(0.0, 1.0 - corruption_score)
    }
    
    if is_corrupted:
        print(f"🚨 TEXT CORRUPTION DETECTED!")
        print(f"   Corruption score: {corruption_score:.3f} (threshold: {corruption_threshold})")
        print(f"   Primary indicators: {[ind['type'] for ind in corruption_indicators[:3]]}")
        print(f"   Confidence: {corruption_details['confidence']:.3f}")
    else:
        print(f"✅ Text appears clean (corruption score: {corruption_score:.3f})")
    
    return is_corrupted, cleaned_text, corruption_details

def detect_tamil_corruption_conservative(text):
    """
    Conservative corruption detection specifically for Tamil text.
    Only flags obvious corruption patterns.
    """
    if not text or len(text.strip()) < 10:
        return False, text, {'reason': 'text_too_short'}
    
    # Patterns that indicate genuine corruption
    problematic_patterns = [
        r'__[A-Z_]+__',  # Placeholder patterns
        r'\[CAPITAL_WORD\]',  # Bracket placeholders
        r'\d+_\[CAPITAL_WORD\]_CAPWORD',  # Complex placeholders
        r'\*\.?\d*\*',  # Asterisk patterns like *.21*
    ]
    
    corruption_indicators = []
    
    # Check for placeholder patterns
    for pattern in problematic_patterns:
        if re.search(pattern, text):
            corruption_indicators.append(f'placeholder_pattern: {pattern}')
    
    # Check for excessive word repetition (very conservative)
    words = text.split()
    if len(words) > 5:
        word_freq = {}
        for word in words:
            if len(word) > 2:  # Only count meaningful words
                word_freq[word.lower()] = word_freq.get(word.lower(), 0) + 1
        
        # Only flag if a word appears more than 40% of the time
        max_freq = max(word_freq.values()) if word_freq else 0
        repetition_ratio = max_freq / len(words) if len(words) > 0 else 0
        
        if repetition_ratio > 0.4:
            corruption_indicators.append(f'excessive_repetition: {repetition_ratio:.2f}')
    
    # Only consider corrupted if we have clear indicators
    is_corrupted = len(corruption_indicators) > 0
    
    if is_corrupted:
        cleaned_text = clean_tamil_text_optimized(text)
    else:
        cleaned_text = text
    
    return is_corrupted, cleaned_text, {
        'indicators': corruption_indicators,
        'confidence': len(corruption_indicators) * 0.2,  # Lower confidence
        'reason': 'conservative_tamil_detection'
    }

def clean_tamil_text_optimized(text):
    """
    Optimized Tamil text cleaning that preserves maximum content.
    """
    if not text or not isinstance(text, str):
        return text
    
    # Skip if too short
    if len(text.strip()) < 10:
        return text
    
    cleaned_text = text
    
    # Only remove genuine problematic patterns
    problematic_patterns = [
        r'__[A-Z_]+__',
        r'\[CAPITAL_WORD\]',
        r'\d+_\[CAPITAL_WORD\]_CAPWORD',
        r'\*\.?\d*\*',
    ]
    
    for pattern in problematic_patterns:
        cleaned_text = re.sub(pattern, '', cleaned_text)
    
    # Fix only obvious repetition (3+ consecutive identical words)
    cleaned_text = re.sub(r'(\b[\u0B80-\u0BFF]+\b)(\s+\1){2,}', r'\1', cleaned_text)
    
    # Clean up excessive whitespace
    cleaned_text = re.sub(r'\s{3,}', ' ', cleaned_text)
    cleaned_text = re.sub(r'\n{3,}', '\n\n', cleaned_text)
    
    # Fix scattered numbers only if clearly breaking sentences
    cleaned_text = re.sub(r'\.(\d+)\.(\d+)\.', '. ', cleaned_text)
    
    # Normalize Unicode
    import unicodedata
    cleaned_text = unicodedata.normalize('NFC', cleaned_text)
    
    return cleaned_text.strip()

# Add a test endpoint for corruption detection
@app.route('/api/test-corruption-detection', methods=['POST'])
def test_corruption_detection():
    """
    Test endpoint to verify corruption detection and cleaning for multilingual text.
    """
    try:
        data = request.get_json() or {}
        test_text = data.get('text', '').strip()
        
        if not test_text:
            return jsonify({
                "success": False,
                "error": "Text is required for testing"
            }), 400
        
        # Apply corruption detection
        is_corrupted, cleaned_text, corruption_details = detect_text_corruption(test_text)
        
        # Get character diversity analysis
        diversity_analysis = calculate_script_aware_char_diversity(test_text)
        
        # Prepare response
        response_data = {
            "success": True,
            "original_text": test_text,
            "is_corrupted": is_corrupted,
            "cleaned_text": cleaned_text,
            "corruption_details": corruption_details,
            "diversity_analysis": {
                "script_type": diversity_analysis['script_type'],
                "normalized_diversity": diversity_analysis['normalized_diversity'],
                "complexity_score": diversity_analysis['complexity_score'],
                "script_composition": diversity_analysis['script_composition']
            },
            "text_stats": {
                "original_length": len(test_text),
                "cleaned_length": len(cleaned_text),
                "characters_removed": len(test_text) - len(cleaned_text)
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/test-telugu-processing', methods=['POST'])
def test_telugu_processing():
    """
    Specific test endpoint for Telugu text processing and corruption detection.
    """
    try:
        data = request.get_json() or {}
        telugu_text = data.get('text', '').strip()
        
        if not telugu_text:
            # Use the example text you provided for testing
            telugu_text = """అందించిన సందర్భం భారతదేశంలో భారతదేశంలో స్టార్టప్‌ల వెంచర్ క్యాపిటల్ క్యాపిటల్ (విసి) లేదా ఏంజెల్ ఇన్వెస్టర్స్ నుండి నిధులను సేకరించడం గురించి. స్పోర్ట్స్ ట్రైనింగ్ ప్రోగ్రామ్స్, పబ్లిక్ పబ్లిక్ ఫిర్యాదుల సమావేశాలు మరియు బీ అటాక్ సంఘటన వంటి చర్చలు మూలాలు మూలాలు పేర్కొన్నాయి, కాని స్టార్టప్ ఎవరూ ఎవరూ జోడించలేదు."""
        
        print(f"🧪 Testing Telugu text processing...")
        print(f"   Original text length: {len(telugu_text)} characters")
        
        # Step 1: Word repetition detection
        word_corrupted, word_cleaned = detect_word_repetition(telugu_text)
        
        # Step 2: Character diversity analysis
        diversity_analysis = calculate_script_aware_char_diversity(telugu_text)
        
        # Step 3: Full corruption detection
        is_corrupted, fully_cleaned, corruption_details = detect_text_corruption(telugu_text)
        
        # Step 4: Test translation simulation (if available)
        translation_test = None
        try:
            from services.translation_service import TranslationService
            translation_service = TranslationService()
            
            # Test translation to English and back
            to_english = translation_service.translate_text(fully_cleaned, target_lang='en', source_lang='te')
            if to_english and to_english.get('translated_text'):
                back_to_telugu = translation_service.translate_text(
                    to_english['translated_text'], 
                    target_lang='te', 
                    source_lang='en'
                )
                
                if back_to_telugu and back_to_telugu.get('translated_text'):
                    # Test corruption in translated result
                    trans_corrupted, trans_cleaned, trans_details = detect_text_corruption(back_to_telugu['translated_text'])
                    
                    translation_test = {
                        "english_translation": to_english['translated_text'],
                        "back_to_telugu": back_to_telugu['translated_text'],
                        "translation_corrupted": trans_corrupted,
                        "translation_cleaned": trans_cleaned,
                        "translation_corruption_details": trans_details
                    }
        except ImportError:
            translation_test = {"error": "Translation service not available"}
        
        # Prepare comprehensive response
        response_data = {
            "success": True,
            "test_type": "Telugu Text Processing",
            "original_text": telugu_text,
            "processing_steps": {
                "1_word_repetition": {
                    "detected": word_corrupted,
                    "cleaned_text": word_cleaned,
                    "words_removed": len(telugu_text.split()) - len(word_cleaned.split())
                },
                "2_diversity_analysis": {
                    "script_type": diversity_analysis['script_type'],
                    "normalized_diversity": diversity_analysis['normalized_diversity'],
                    "complexity_score": diversity_analysis['complexity_score'],
                    "script_composition": diversity_analysis['script_composition']
                },
                "3_full_corruption_detection": {
                    "is_corrupted": is_corrupted,
                    "cleaned_text": fully_cleaned,
                    "corruption_score": corruption_details['corruption_score'],
                    "indicators": [ind['type'] for ind in corruption_details['indicators']],
                    "confidence": corruption_details['confidence']
                },
                "4_translation_test": translation_test
            },
            "summary": {
                "original_length": len(telugu_text),
                "final_cleaned_length": len(fully_cleaned),
                "characters_removed": len(telugu_text) - len(fully_cleaned),
                "corruption_detected": is_corrupted,
                "script_properly_detected": diversity_analysis['script_type'] == 'telugu'
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "test_type": "Telugu Text Processing"
        }), 500

# Add a simple health check endpoint
@app.route('/api/health', methods=['GET'])
def health_check():
    """
    Enhanced health check endpoint to verify the server and upload services are running.
    """
    try:
        # Check upload service availability
        upload_services = {}

        try:
            from services.youtube_processor import process_youtube_url
            upload_services['youtube_processor'] = True
        except ImportError:
            upload_services['youtube_processor'] = False

        try:
            from services.article_processor import process_article_url
            upload_services['article_processor'] = True
        except ImportError:
            upload_services['article_processor'] = False

        try:
            from services.pdf_processor import process_pdf_file
            upload_services['pdf_processor'] = True
        except ImportError:
            upload_services['pdf_processor'] = False

        try:
            from services.document_processor import process_document_file
            upload_services['document_processor'] = True
        except ImportError:
            upload_services['document_processor'] = False

        try:
            from services.audio_proccessor import process_audio_file
            upload_services['audio_processor'] = True
        except ImportError:
            upload_services['audio_processor'] = False

        # Check DeepSeek API availability
        deepseek_status = {
            "api_key_configured": bool(DEEPSEEK_API_KEY),
            "client_initialized": deepseek_client is not None
        }

        # Prepare health data
        health_data = {
            "server_status": "running",
            "faiss_data_dir": FAISS_DATA_DIR,
            "default_embed_model": DEFAULT_EMBED_MODEL,
            "deepseek_status": deepseek_status,
            "upload_services": upload_services,
            "available_endpoints": [
                "/api/process_youtube",
                "/api/process_article",
                "/api/process_pdf",
                "/api/process_document",
                "/api/process_audio",
                "/api/upload-csv",
                "/api/query-faiss",
                "/financial_query"
            ]
        }

        # Calculate service availability percentage
        total_services = len(upload_services)
        active_services = sum(1 for status in upload_services.values() if status)
        service_availability = (active_services / total_services * 100) if total_services > 0 else 100

        metadata = {
            "service_availability_percentage": round(service_availability, 1),
            "total_services": total_services,
            "active_services": active_services,
            "server_uptime": "Available", # Could be enhanced with actual uptime tracking
            "api_version": "1.0.0"
        }

        response, status_code = ResponseFormatter.success_response(
            data=health_data,
            message="FAISS backend server is running and healthy",
            metadata=metadata
        )

        return jsonify(response), status_code

    except Exception as e:
        response, status_code = ResponseFormatter.error_response(
            error=str(e),
            error_type="health_check_error",
            status_code=500
        )
        return jsonify(response), status_code

@app.route('/api/translate', methods=['POST'])
def translate_text():
    """
    Translate text using the backend translation service.
    """
    try:
        data = request.get_json() or {}
        text = data.get('text', '').strip()
        source_lang = data.get('source_lang', 'auto')
        target_lang = data.get('target_lang', 'en')

        if not text:
            response, status_code = ResponseFormatter.error_response(
                error="Text is required for translation",
                error_type="validation_error",
                status_code=400
            )
            return jsonify(response), status_code

        # Import NEW translation service (with capital word preservation)
        try:
            from services.google_translate_api_service_new import google_translate_service

            # Check if the new service is available
            if not google_translate_service.is_service_available():
                raise ImportError("Google Translate API service not available")

            # Perform translation using the new service
            translation_result = google_translate_service.translate_text(text, target_lang, source_lang)

            response_data = {
                "original_text": text,
                "translated_text": translation_result.get('translated_text', text),
                "source_language": translation_result.get('source_language', source_lang),
                "target_language": translation_result.get('target_language', target_lang),
                "translation_provider": "google_translate_api_new",
                "cached": False  # New service doesn't use caching yet
            }

            response, status_code = ResponseFormatter.success_response(
                data=response_data,
                message="Translation completed successfully"
            )
            return jsonify(response), status_code

        except ImportError:
            # Fallback to old translation service if new one is not available
            print("⚠️ New Google Translate API service not available, falling back to old service")
            try:
                from services.translation_service import translation_service

                # Perform translation using the old service (with disabled CAPWORD tokens)
                translation_result = translation_service.translate_text(text, target_lang, source_lang)

                response_data = {
                    "original_text": text,
                    "translated_text": translation_result.get('translated_text', text),
                    "source_language": translation_result.get('source_language', source_lang),
                    "target_language": translation_result.get('target_language', target_lang),
                    "translation_provider": translation_result.get('translation_provider', 'fallback_old'),
                    "cached": translation_result.get('cached', False)
                }

                response, status_code = ResponseFormatter.success_response(
                    data=response_data,
                    message="Translation completed using fallback service"
                )
                return jsonify(response), status_code

            except ImportError:
                response, status_code = ResponseFormatter.error_response(
                    error="No translation service available",
                    error_type="service_unavailable",
                    status_code=503
                )
                return jsonify(response), status_code

    except Exception as e:
        response, status_code = ResponseFormatter.error_response(
            error=str(e),
            error_type="translation_error",
            status_code=500
        )
        return jsonify(response), status_code

@app.route('/api/test-deepseek', methods=['POST'])
def test_deepseek():
    """
    Test endpoint to verify DeepSeek API functionality.
    """
    try:
        data = request.get_json() or {}
        test_query = data.get('query', 'What is artificial intelligence?')
        test_answer = data.get('answer', 'Artificial intelligence is a field of computer science focused on creating systems that can perform tasks that typically require human intelligence.')

        print(f"🧪 Testing DeepSeek API with query: {test_query}")

        # Get language parameter for testing
        test_language = data.get('language', 'English')

        # Test the related questions generation
        related_questions = generate_related_questions(test_query, test_answer, test_language)

        return jsonify({
            "success": True,
            "message": "DeepSeek API test completed",
            "test_query": test_query,
            "test_answer": test_answer,
            "related_questions": related_questions,
            "api_key_configured": bool(DEEPSEEK_API_KEY),
            "questions_count": len(related_questions)
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "api_key_configured": bool(DEEPSEEK_API_KEY)
        }), 500

@app.route('/api/test-translation', methods=['POST'])
def test_translation():
    """
    Test endpoint to verify translation functionality.
    """
    try:
        data = request.get_json() or {}
        text = data.get('text', 'Hello, how are you?')
        target_language = data.get('target_language', 'ta')

        # Import translation service
        try:
            from services.translation_service import translation_service

            # Test language detection
            detected_language = translation_service.detect_language(text)

            # Test translation
            translation_result = translation_service.translate_text(text, target_language)

            # Test response translation
            test_response = {
                'ai_response': 'This is a test response for translation.',
                'related_questions': ['What is this?', 'How does it work?', 'Why is it important?']
            }

            translated_response = translation_service.translate_response_data(test_response, target_language)

            return jsonify({
                "success": True,
                "message": "Translation test completed",
                "original_text": text,
                "detected_language": detected_language,
                "target_language": target_language,
                "translation_result": translation_result,
                "test_response_translation": translated_response,
                "cache_stats": translation_service.get_cache_stats()
            })

        except ImportError as e:
            return jsonify({
                "success": False,
                "error": f"Translation service not available: {str(e)}"
            }), 500

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

# Add CORS preflight handler for all API routes
@app.before_request
def handle_preflight():
    if request.method == "OPTIONS":
        response = jsonify({})
        origin = request.headers.get('Origin')
        allowed_origins = [
            'http://localhost:3000',
            'http://localhost:3001',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:3001',
            'http://**************:3000',
            'http://**************:3001'
        ]

        if origin in allowed_origins:
            response.headers.add("Access-Control-Allow-Origin", origin)
        else:
            response.headers.add("Access-Control-Allow-Origin", "http://localhost:3000")

        response.headers.add('Access-Control-Allow-Headers', "Content-Type,Authorization,X-Requested-With,Origin")
        response.headers.add('Access-Control-Allow-Methods', "GET,PUT,POST,DELETE,OPTIONS")
        response.headers.add('Access-Control-Allow-Credentials', 'true')
        return response

def create_faiss_index(index_name: str, dimension: int = None, embed_model: str = None) -> Dict[str, Any]:
    """
    Create a new FAISS index directory structure with the given name and dimension.

    Args:
        index_name: Name for the new index (will be used as folder name)
        dimension: Vector dimension (default: None, will use dimension from embedding model)
        embed_model: Name of the embedding model to use (default: None, will use DEFAULT_EMBED_MODEL)

    Returns:
        Dict with success status and error message if any
    """
    # Determine the dimension based on the embedding model
    if dimension is None:
        model_name = embed_model or DEFAULT_EMBED_MODEL
        if model_name in EMBEDDING_MODELS:
            dimension = EMBEDDING_MODELS[model_name]["dimension"]
        else:
            dimension = EMBEDDING_MODELS[DEFAULT_EMBED_MODEL]["dimension"]
        print(f"Using dimension {dimension} from model {model_name}")

    try:
        # Create index directory path
        index_dir = os.path.join(FAISS_DATA_DIR, index_name)
        faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
        metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

        # Check if index already exists
        if os.path.exists(faiss_file_path) and os.path.exists(metadata_file_path):
            print(f"FAISS index '{index_name}' already exists. Using existing index...")
            return {"success": True, "message": "Using existing index"}

        # Create directory if it doesn't exist
        os.makedirs(index_dir, exist_ok=True)

        # Create empty FAISS index
        # Using IndexFlatIP for inner product (cosine similarity after normalization)
        index = faiss.IndexFlatIP(dimension)

        # Save empty index
        faiss.write_index(index, faiss_file_path)

        # Create empty metadata file
        with open(metadata_file_path, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

        print(f"Successfully created FAISS index: {index_name}")
        return {"success": True}

    except Exception as e:
        error_message = str(e)
        print(f"Error creating FAISS index: {error_message}")
        return {"success": False, "error": error_message, "error_type": "create_index_error"}


def load_faiss_index(index_name: str) -> tuple:
    """
    Load FAISS index and metadata from files.
    Special handling for 'default' index which may use different file names.

    Args:
        index_name: Name of the index to load

    Returns:
        tuple: (faiss_index, metadata_store, success)
    """
    try:
        index_dir = os.path.join(FAISS_DATA_DIR, index_name)

        # For default index, try both naming conventions
        if index_name == "default":
            # Try standard naming first
            faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
            metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

            # If standard naming doesn't exist, try legacy naming
            if not (os.path.exists(faiss_file_path) and os.path.exists(metadata_file_path)):
                faiss_file_path = os.path.join(index_dir, "default1.faiss")
                metadata_file_path = os.path.join(index_dir, "default1.json")
                print(f"Using legacy file names for default index: {faiss_file_path}, {metadata_file_path}")
        else:
            # For other indexes, use standard naming
            faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
            metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

        if not os.path.exists(faiss_file_path) or not os.path.exists(metadata_file_path):
            print(f"FAISS index files not found for '{index_name}': {faiss_file_path}, {metadata_file_path}")
            return None, [], False

        # Load FAISS index
        faiss_index = faiss.read_index(faiss_file_path)

        # Load metadata
        with open(metadata_file_path, 'r', encoding='utf-8') as f:
            metadata_store = json.load(f)

        print(f"✅ Successfully loaded FAISS index '{index_name}' with {faiss_index.ntotal} vectors")
        return faiss_index, metadata_store, True

    except Exception as e:
        print(f"Error loading FAISS index {index_name}: {e}")
        return None, [], False

def save_faiss_index(index_name: str, faiss_index, metadata_store: list) -> bool:
    """
    Save FAISS index and metadata to files.

    Args:
        index_name: Name of the index
        faiss_index: FAISS index object
        metadata_store: List of metadata dictionaries

    Returns:
        bool: Success status
    """
    try:
        index_dir = os.path.join(FAISS_DATA_DIR, index_name)
        faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
        metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

        # Ensure directory exists
        os.makedirs(index_dir, exist_ok=True)

        # Save FAISS index
        faiss.write_index(faiss_index, faiss_file_path)

        # Save metadata
        with open(metadata_file_path, 'w', encoding='utf-8') as f:
            json.dump(metadata_store, f, ensure_ascii=False, indent=2)

        return True

    except Exception as e:
        print(f"Error saving FAISS index {index_name}: {e}")
        return False



def chunk_text(text: str, size: int = 500) -> List[str]:
    """
    Split text into chunks of approximately equal size.

    Args:
        text: Text to split
        size: Maximum chunk size

    Returns:
        List of text chunks
    """
    chunks, start = [], 0
    if not text or not isinstance(text, str):
        return []

    while start < len(text):
        end = min(start + size, len(text))
        while end > start and end < len(text) and not text[end - 1].isspace():
            end -= 1
        if end == start:
            end = min(start + size, len(text))
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        start = end
    return chunks

def process_csv_data(csv_data: str, index_name: str, update_mode: str = None,
                upload_id: str = None, client_email: str = None, embed_model: str = None, file_name: str = None) -> Dict[str, Any]:
    """
    Process CSV data and upload to FAISS and database.

    Args:
        csv_data: CSV data as string
        index_name: Name of the FAISS index to use
        update_mode: Mode for handling existing indexes ('update' to keep existing data, 'new' to delete and start fresh)
        upload_id: Unique identifier for this upload process (for cancellation)
        client_email: Client email for tracking (default: None)
        embed_model: Name of the embedding model to use (default: None, will use DEFAULT_EMBED_MODEL)
        file_name: Original file name for tracking (default: None)

    Returns:
        Dict with processing results
    """
    # Use the specified embedding model or default
    model_name = embed_model or DEFAULT_EMBED_MODEL
    if model_name not in EMBEDDING_MODELS:
        print(f"Warning: Unknown model '{model_name}'. Using default model '{DEFAULT_EMBED_MODEL}'")
        model_name = DEFAULT_EMBED_MODEL

    # Get the embedder for this model
    embedder = get_embedder(model_name)
    print(f"Using embedding model: {model_name} with dimension {EMBEDDING_MODELS[model_name]['dimension']}")
    try:

        # Parse CSV data
        try:
            # Try different parsing strategies with improved handling of empty rows and column names
            parsing_strategies = [
                # Strategy 1: Default parsing with keep_default_na=True to properly detect empty cells
                lambda: pd.read_csv(io.StringIO(csv_data), keep_default_na=True),

                # Strategy 2: With index_col=0 for unnamed first column
                lambda: pd.read_csv(io.StringIO(csv_data), index_col=0, keep_default_na=True),

                # Strategy 3: With escape characters
                lambda: pd.read_csv(io.StringIO(csv_data), escapechar='\\', quotechar='"', doublequote=True, keep_default_na=True),

                # Strategy 4: With different separator
                lambda: pd.read_csv(io.StringIO(csv_data), sep=',', engine='python', keep_default_na=True),

                # Strategy 5: With error handling
                lambda: pd.read_csv(io.StringIO(csv_data), on_bad_lines='skip', keep_default_na=True)
            ]

            df = None
            for i, strategy in enumerate(parsing_strategies):
                try:
                    df = strategy()
                    print(f"CSV parsed successfully with strategy {i+1}. Shape: {df.shape}")
                    if not df.empty:
                        # Clean column names - remove whitespace and ensure they're strings
                        df.columns = [str(col).strip() for col in df.columns]

                        # Print column information
                        print(f"Columns: {df.columns.tolist()}")

                        # Count non-empty rows
                        non_empty_rows = 0
                        for _, row in df.iterrows():
                            if any(pd.notna(val) and str(val).strip() != '' for val in row.values):
                                non_empty_rows += 1

                        print(f"Total rows: {len(df)}, Non-empty rows: {non_empty_rows}")

                        if non_empty_rows > 0:
                            # Find first non-empty row to show as sample
                            for idx, row in df.iterrows():
                                if any(pd.notna(val) and str(val).strip() != '' for val in row.values):
                                    print(f"First non-empty row sample (index {idx}): {row.to_dict()}")
                                    break
                            break
                        else:
                            print("Warning: All rows appear to be empty. Will try next parsing strategy.")
                            df = None
                except Exception as e:
                    print(f"Parsing strategy {i+1} failed: {e}")

            if df is None or df.empty:
                raise ValueError("Could not parse CSV data with any strategy or all rows are empty")

        except Exception as e:
            error_message = str(e)
            print(f"Error parsing CSV data: {error_message}")
            return {
                "success": False,
                "error": f"Failed to parse CSV data: {error_message}",
                "error_type": "csv_parse_error"
            }

        # Store CSV data in database with embedding model information
        try:
            # Get the dimension for the embedding model
            dimension = EMBEDDING_MODELS[model_name]["dimension"] if model_name in EMBEDDING_MODELS else None

            db_success, db_message, db_info = database.create_table_from_csv(
                csv_data,
                index_name,
                client_email,
                embedding_model=model_name,
                embedding_dimension=dimension,
                file_name=file_name
            )

            if not db_success:
                print(f"Warning: Failed to store CSV data in database: {db_message}")
                # Continue with FAISS processing even if database storage fails
            else:
                print(f"Successfully stored CSV data in database: {db_message}")
        except Exception as e:
            print(f"Error storing CSV data in database: {e}")
            # Continue with FAISS processing even if database storage fails

        # Initialize FAISS index
        try:
            # Get the dimension for the embedding model
            dimension = EMBEDDING_MODELS[model_name]["dimension"]

            # If update_mode is 'new', delete the existing index and create a new one
            if update_mode == 'new':
                try:
                    index_dir = os.path.join(FAISS_DATA_DIR, index_name)
                    if os.path.exists(index_dir):
                        print(f"Deleting existing index '{index_name}' as per 'new' update mode...")
                        import shutil
                        shutil.rmtree(index_dir)
                        print(f"Successfully deleted index '{index_name}'")

                    # Create a new index
                    create_result = create_faiss_index(index_name, dimension, model_name)
                    if not create_result["success"]:
                        return {
                            "success": False,
                            "error": f"Failed to create new index: {create_result['error']}",
                            "error_type": "index_creation_error"
                        }
                    print(f"Successfully created new index '{index_name}'")
                except Exception as e:
                    error_message = str(e)
                    print(f"Error handling 'new' update mode: {error_message}")
                    return {
                        "success": False,
                        "error": f"Failed to recreate index: {error_message}",
                        "error_type": "index_recreation_error"
                    }

            # Load or create FAISS index
            faiss_index, metadata_store, success = load_faiss_index(index_name)
            if not success:
                # Create new index if it doesn't exist
                create_result = create_faiss_index(index_name, dimension, model_name)
                if not create_result["success"]:
                    return {
                        "success": False,
                        "error": f"Failed to create FAISS index: {create_result['error']}",
                        "error_type": "faiss_access_error"
                    }
                faiss_index, metadata_store, success = load_faiss_index(index_name)
                if not success:
                    return {
                        "success": False,
                        "error": "Failed to load newly created FAISS index",
                        "error_type": "faiss_access_error"
                    }
        except Exception as e:
            error_message = str(e)
            print(f"Error initializing FAISS index: {error_message}")
            return {
                "success": False,
                "error": f"Failed to access FAISS index: {error_message}",
                "error_type": "faiss_access_error"
            }

        # Process data in batches
        vectors = []
        new_metadata = []
        total_vectors = 0

        # Register this upload in active_uploads if upload_id is provided
        if upload_id:
            with active_uploads_lock:
                active_uploads[upload_id] = {
                    "status": "processing",
                    "total_rows": len(df),
                    "processed_rows": 0,
                    "total_vectors": 0,
                    "index_name": index_name,
                    "start_time": time.time(),
                    "cancelled": False
                }

        # Function to prepare a vector from a row and chunk
        def prepare_vector(row_idx, chunk_idx, chunk, metadata):
            try:
                # Check if upload has been cancelled
                if upload_id and upload_id in active_uploads:
                    with active_uploads_lock:
                        if active_uploads[upload_id]["cancelled"]:
                            return None

                # Generate embedding for the chunk using the selected model
                vector = embedder.embed_documents([chunk])[0]
                vector_array = np.array(vector, dtype='float32')

                # Create a truly unique ID for this vector by adding a UUID component
                # This ensures that even if the same CSV is uploaded multiple times in "update" mode,
                # we won't overwrite existing vectors but will add new ones
                unique_suffix = str(uuid.uuid4())[:8]  # Use first 8 chars of UUID for brevity
                vector_id = f"{index_name}-{row_idx}-chunk-{chunk_idx}-{unique_suffix}"

                # Log the vector ID creation for debugging
                if row_idx % 50 == 0 and chunk_idx == 0:  # Log only occasionally to avoid excessive output
                    print(f"Created unique vector ID: {vector_id} for row {row_idx}, chunk {chunk_idx}")

                # Add embedding model info to metadata
                metadata_with_model = {
                    **metadata,
                    "embedding_model": model_name,
                    "embedding_dimension": EMBEDDING_MODELS[model_name]["dimension"],
                    "upload_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),  # Add timestamp to track when this vector was created
                    "row_idx": row_idx,
                    "chunk_idx": chunk_idx,
                    "chunk_text": chunk,
                    "vector_id": vector_id
                }

                # Return the vector data (vector array and metadata separately for FAISS)
                return {
                    "vector": vector_array,
                    "metadata": metadata_with_model
                }
            except Exception as e:
                print(f"Error preparing vector for row {row_idx}, chunk {chunk_idx}: {e}")
                return None

        # Function to process a single row
        def process_row(row_idx, row):
            try:
                # Check if upload has been cancelled
                if upload_id and upload_id in active_uploads:
                    with active_uploads_lock:
                        if active_uploads[upload_id]["cancelled"]:
                            return None

                # Check if row is empty (all values are NaN or empty strings)
                is_empty = True
                for val in row.values:
                    if pd.notna(val) and str(val).strip() != '':
                        is_empty = False
                        break

                # Skip empty rows
                if is_empty:
                    print(f"Skipping empty row at index {row_idx}")
                    return []

                # Extract content and metadata from the row
                content = ""
                metadata = {}

                # Extract specific fields if they exist
                if 'Content' in row and pd.notna(row['Content']):
                    content = str(row['Content'])
                if 'Summary' in row and pd.notna(row['Summary']):
                    metadata["summary"] = str(row['Summary'])[:500]
                if 'URL' in row and pd.notna(row['URL']):
                    metadata["url"] = str(row['URL'])[:500]
                if 'Sentiment' in row and pd.notna(row['Sentiment']):
                    metadata["sentiment"] = str(row['Sentiment'])

                # Store all column values as metadata using column names as keys
                # This ensures we preserve the original column structure
                for col, val in row.items():
                    if pd.notna(val) and str(val).strip() != '':
                        # Limit metadata value length to 500 chars to avoid Pinecone limits
                        metadata[str(col)] = str(val)[:500]

                # If content is empty, try to build it from all columns
                if not content:
                    content_parts = []
                    for col, val in row.items():
                        if pd.notna(val) and str(col).strip() != '' and str(val).strip() != '':
                            content_parts.append(f"{col}: {val}")
                    content = " ".join(content_parts)

                # If still empty after trying to build from columns, use a default message
                if not content.strip():
                    content = f"Row {row_idx} from {index_name} dataset"
                    print(f"Warning: Empty content for row {row_idx}")

                # Add row_idx to metadata
                metadata["row_idx"] = row_idx

                # Split content into chunks
                chunks = chunk_text(content)
                if not chunks:
                    # If no chunks, create one with the content
                    chunks = [content]

                # Create vectors for each chunk
                vectors = []
                for chunk_idx, chunk in enumerate(chunks):
                    vector_data = prepare_vector(row_idx, chunk_idx, chunk, metadata)
                    if vector_data:
                        vectors.append(vector_data)

                return vectors
            except Exception as e:
                print(f"Error processing row {row_idx}: {e}")
                print(f"Row data: {row}")
                return []

        # Process rows in parallel
        print(f"Starting parallel processing of {len(df)} rows...")
        start_time = time.time()

        with ThreadPoolExecutor(max_workers=5) as executor:
            # Submit all row processing tasks
            futures = [executor.submit(process_row, idx, row) for idx, row in df.iterrows()]
            total_futures = len(futures)
            completed_futures = 0

            # Process completed futures
            for future in as_completed(futures):
                completed_futures += 1
                if completed_futures % 100 == 0 or completed_futures == total_futures:
                    print(f"Processed {completed_futures}/{total_futures} rows ({(completed_futures/total_futures)*100:.1f}%)")
                # Check if upload has been cancelled
                if upload_id and upload_id in active_uploads:
                    with active_uploads_lock:
                        if active_uploads[upload_id]["cancelled"]:
                            print(f"Upload {upload_id} was cancelled. Stopping processing.")
                            # Clean up futures to prevent further processing
                            for f in futures:
                                f.cancel()
                            # Update status
                            active_uploads[upload_id]["status"] = "cancelled"
                            return {
                                "success": False,
                                "error": "Upload was cancelled by user",
                                "error_type": "upload_cancelled",
                                "total_vectors": total_vectors,
                                "upload_id": upload_id
                            }

                # Get the result from the future (now returns a list of vectors)
                vector_data_list = future.result()

                # Add all vectors to the batch
                if vector_data_list:
                    for vector_data in vector_data_list:
                        if vector_data:
                            vectors.append(vector_data["vector"])
                            new_metadata.append(vector_data["metadata"])

                # Process in batches when we reach the batch size
                while len(vectors) >= BATCH_SIZE:

                    # Check again if upload has been cancelled before database operation
                    if upload_id and upload_id in active_uploads:
                        with active_uploads_lock:
                            if active_uploads[upload_id]["cancelled"]:
                                print(f"Upload {upload_id} was cancelled before batch processing. Stopping processing.")
                                # Clean up futures to prevent further processing
                                for f in futures:
                                    if not f.done():
                                        f.cancel()
                                # Update status
                                active_uploads[upload_id]["status"] = "cancelled"
                                return {
                                    "success": False,
                                    "error": "Upload was cancelled by user",
                                    "error_type": "upload_cancelled",
                                    "total_vectors": total_vectors,
                                    "upload_id": upload_id
                                }

                        # Proceed with batch processing if not cancelled
                        batch_vectors = vectors[:BATCH_SIZE]
                        batch_metadata = new_metadata[:BATCH_SIZE]
                        vectors = vectors[BATCH_SIZE:]
                        new_metadata = new_metadata[BATCH_SIZE:]

                        try:
                            # Log the update mode for clarity
                            mode_msg = "adding new vectors" if update_mode == 'update' or update_mode is None else "replacing existing vectors"
                            print(f"Processing batch in {update_mode or 'update'} mode ({mode_msg})...")

                            # Convert to numpy array and normalize for cosine similarity
                            batch_array = np.vstack(batch_vectors)
                            faiss.normalize_L2(batch_array)

                            # Add vectors to FAISS index
                            faiss_index.add(batch_array)

                            # Add metadata to metadata store
                            metadata_store.extend(batch_metadata)

                            total_vectors += len(batch_vectors)
                            print(f"Successfully processed batch of {len(batch_vectors)} vectors. Total: {total_vectors}")

                            # Update progress in active_uploads
                            if upload_id and upload_id in active_uploads:
                                with active_uploads_lock:
                                    active_uploads[upload_id]["total_vectors"] = total_vectors
                                    active_uploads[upload_id]["processed_rows"] += len(batch_vectors)
                        except Exception as e:
                            print(f"Error processing batch: {e}")
                            # Continue processing despite errors

        # Process any remaining vectors
        if vectors:
            # Check again if upload has been cancelled before final processing
            if upload_id and upload_id in active_uploads:
                with active_uploads_lock:
                    if active_uploads[upload_id]["cancelled"]:
                        print(f"Upload {upload_id} was cancelled before final processing. Stopping processing.")
                        # Update status
                        active_uploads[upload_id]["status"] = "cancelled"
                        return {
                            "success": False,
                            "error": "Upload was cancelled by user",
                            "error_type": "upload_cancelled",
                            "total_vectors": total_vectors,
                            "upload_id": upload_id
                        }

            # Proceed with final processing if not cancelled
            try:
                # Log the update mode for clarity
                mode_msg = "adding new vectors" if update_mode == 'update' or update_mode is None else "replacing existing vectors"
                print(f"Processing final batch in {update_mode or 'update'} mode ({mode_msg})...")

                # Convert to numpy array and normalize for cosine similarity
                final_array = np.vstack(vectors)
                faiss.normalize_L2(final_array)

                # Add vectors to FAISS index
                faiss_index.add(final_array)

                # Add metadata to metadata store
                metadata_store.extend(new_metadata)

                total_vectors += len(vectors)
                print(f"Successfully processed final batch of {len(vectors)} vectors. Total: {total_vectors}")

                # Update progress in active_uploads
                if upload_id and upload_id in active_uploads:
                    with active_uploads_lock:
                        active_uploads[upload_id]["total_vectors"] = total_vectors
                        active_uploads[upload_id]["processed_rows"] += len(vectors)
            except Exception as e:
                print(f"Error processing final batch: {e}")
                # Continue despite errors

        # Save the updated FAISS index and metadata
        try:
            save_success = save_faiss_index(index_name, faiss_index, metadata_store)
            if save_success:
                print(f"Successfully saved FAISS index with {total_vectors} vectors")
            else:
                print(f"Warning: Failed to save FAISS index")
        except Exception as e:
            print(f"Error saving FAISS index: {e}")
            # Continue despite save errors

        # Calculate processing time and log completion
        end_time = time.time()
        processing_time = end_time - start_time
        print(f"Processing completed in {processing_time:.2f} seconds. Total vectors: {total_vectors}, Total rows: {len(df)}")

        # Update status to complete
        if upload_id and upload_id in active_uploads:
            with active_uploads_lock:
                active_uploads[upload_id]["status"] = "complete"
                active_uploads[upload_id]["end_time"] = end_time
                active_uploads[upload_id]["processing_time"] = processing_time
                print(f"Upload {upload_id} marked as complete.")

        return {
            "success": True,
            "index_name": index_name,
            "total_vectors": total_vectors,
            "total_rows": len(df),
            "processing_time_seconds": processing_time
        }
    except Exception as e:
        print(f"Error processing CSV data: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@app.route('/api/upload-csv', methods=['POST'])
def upload_csv():
    """
    Endpoint to handle CSV file uploads and store in FAISS with professional response formatting.
    """
    try:
        # Validate file upload
        if 'file' not in request.files:
            response, status_code = ResponseFormatter.error_response(
                error="No file provided in the request",
                error_type="validation_error"
            )
            return jsonify(response), status_code

        file = request.files['file']

        # Validate file selection
        if file.filename == '':
            response, status_code = ResponseFormatter.error_response(
                error="No file selected for upload",
                error_type="validation_error"
            )
            return jsonify(response), status_code

        # Validate file type
        if not file.filename.lower().endswith('.csv'):
            response, status_code = ResponseFormatter.error_response(
                error="Invalid file type. Only CSV files are supported",
                error_type="validation_error",
                details={"supported_formats": [".csv"], "received_file": file.filename}
            )
            return jsonify(response), status_code

        # Extract and validate form parameters
        client = request.form.get('client')
        index_name = request.form.get('index_name')
        update_mode = request.form.get('update_mode')  # 'update' or 'new'
        embed_model = request.form.get('embed_model', DEFAULT_EMBED_MODEL)

        # Validate required parameters
        if not index_name:
            response, status_code = ResponseFormatter.error_response(
                error="Index name is required for CSV upload",
                error_type="validation_error",
                details={"required_fields": ["index_name"]}
            )
            return jsonify(response), status_code

        # Validate client email is provided
        if not client or not client.strip():
            response, status_code = ResponseFormatter.error_response(
                error="Client email is required for CSV upload",
                error_type="validation_error",
                details={"required_fields": ["client"]}
            )
            return jsonify(response), status_code

        # Validate update mode if provided
        if update_mode and update_mode not in ['update', 'new']:
            response, status_code = ResponseFormatter.error_response(
                error="Invalid update mode specified",
                error_type="validation_error",
                details={"valid_modes": ["update", "new"], "received": update_mode}
            )
            return jsonify(response), status_code

        # Log processing information
        print(f"📁 Processing CSV upload for client: {client or 'anonymous'}")
        print(f"🏷️  Index name: {index_name}")
        print(f"🔄 Update mode: {update_mode or 'update (default)'}")
        print(f"🧠 Embedding model: {embed_model}")

        # Read file content
        file_content = file.read().decode('utf-8')

        # Generate a unique upload ID
        upload_id = str(uuid.uuid4())

        # Create or access FAISS index
        index_result = create_faiss_index(index_name, embed_model=embed_model)
        if not index_result["success"]:
            response, status_code = ResponseFormatter.error_response(
                error=index_result.get("error", "Failed to create FAISS index"),
                error_type=index_result.get("error_type", "index_creation_error"),
                details={"index_name": index_name, "embedding_model": embed_model},
                status_code=500
            )
            return jsonify(response), status_code

        # Log processing mode details
        mode_descriptions = {
            'update': f"Adding new vectors to existing index '{index_name}' (preserving existing data)",
            'new': f"Recreating index '{index_name}' from scratch (existing data will be deleted)",
            None: f"Adding new vectors to existing index '{index_name}' (default update mode)"
        }
        print(f"🔧 Processing mode: {mode_descriptions.get(update_mode, mode_descriptions[None])}")

        # Process CSV data and upload to FAISS and database with the update mode, client email, and embedding model
        result = process_csv_data(file_content, index_name, update_mode=update_mode,
                                 upload_id=upload_id, client_email=client, embed_model=embed_model, 
                                 file_name=file.filename)

        if result["success"]:
            # Prepare success response data
            upload_data = {
                "index_name": index_name,
                "vector_count": result["total_vectors"],
                "total_rows_processed": result["total_rows"],
                "processing_time_seconds": result.get("processing_time_seconds", 0),
                "upload_id": upload_id,
                "embedding_model": embed_model,
                "embedding_dimension": EMBEDDING_MODELS[embed_model]["dimension"] if embed_model in EMBEDDING_MODELS else EMBEDDING_MODELS[DEFAULT_EMBED_MODEL]["dimension"],
                "update_mode": update_mode or "update",
                "database_storage_enabled": True
            }

            # Add client information if provided
            if client:
                upload_data["client"] = client

            # Create mode-specific success message
            mode_messages = {
                'update': f"Successfully added {result['total_vectors']} new vectors to existing index '{index_name}'",
                'new': f"Successfully created new index '{index_name}' with {result['total_vectors']} vectors",
                None: f"Successfully added {result['total_vectors']} vectors to index '{index_name}'"
            }

            success_message = mode_messages.get(update_mode, mode_messages[None])

            # Prepare metadata
            metadata = {
                "processing_stats": {
                    "vectors_per_second": round(result["total_vectors"] / max(result.get("processing_time_seconds", 1), 1), 2),
                    "rows_to_vectors_ratio": round(result["total_vectors"] / max(result["total_rows"], 1), 2)
                },
                "storage_info": {
                    "faiss_index_location": f"{FAISS_DATA_DIR}/{index_name}",
                    "database_table": index_name,
                    "retrieval_endpoint": "/api/get-csv-data"
                },
                "next_steps": [
                    f"Query your data using /api/query-faiss with index_name='{index_name}'",
                    f"Retrieve raw CSV data using /api/get-csv-data with index_name='{index_name}'",
                    f"Use /financial_query endpoint for AI-powered responses"
                ]
            }

            response, status_code = ResponseFormatter.success_response(
                data=upload_data,
                message=success_message,
                metadata=metadata
            )

            return jsonify(response), status_code
        else:
            # Handle processing errors
            response, status_code = ResponseFormatter.error_response(
                error=result.get("error", "Unknown error occurred during CSV processing"),
                error_type=result.get("error_type", "processing_error"),
                details={
                    "upload_id": upload_id,
                    "index_name": index_name,
                    "total_vectors_processed": result.get("total_vectors", 0)
                },
                status_code=500
            )
            return jsonify(response), status_code

    except Exception as e:
        # Handle unexpected errors with professional formatting
        response, status_code = ResponseFormatter.error_response(
            error=str(e),
            error_type="unexpected_error",
            details={
                "endpoint": "/api/upload-csv",
                "file_name": request.files.get('file', {}).filename if 'file' in request.files else None
            },
            status_code=500
        )
        return jsonify(response), status_code

@app.route('/api/upload-excel', methods=['POST'])
def upload_excel():
    """
    Endpoint to handle Excel file uploads and store in FAISS with Tamil language support.
    """
    try:
        # Debug logging - print all request data
        print("🔍 DEBUG: Excel upload request received")
        print(f"🔍 DEBUG: Request method: {request.method}")
        print(f"🔍 DEBUG: Request files: {list(request.files.keys())}")
        print(f"🔍 DEBUG: Request form data: {dict(request.form)}")
        print(f"🔍 DEBUG: Request headers: {dict(request.headers)}")

        # Import Excel processor
        from services.excel_processor import (
            validate_excel_file,
            process_excel_data,
            check_duplicate_excel_upload,
            detect_language_from_excel_data
        )

        # Validate file upload
        if 'file' not in request.files:
            print("❌ DEBUG: No 'file' key in request.files")
            response, status_code = ResponseFormatter.error_response(
                error="No file provided in the request",
                error_type="validation_error"
            )
            return jsonify(response), status_code

        file = request.files['file']

        # Validate file selection
        if file.filename == '':
            response, status_code = ResponseFormatter.error_response(
                error="No file selected for upload",
                error_type="validation_error"
            )
            return jsonify(response), status_code

        # Validate file type
        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            response, status_code = ResponseFormatter.error_response(
                error="Invalid file type. Only Excel files (.xlsx, .xls) are supported",
                error_type="validation_error",
                details={"supported_formats": [".xlsx", ".xls"], "received_file": file.filename}
            )
            return jsonify(response), status_code

        # Extract and validate form parameters (support both form data and JSON)
        # Try form data first - check both client_email and client_id for backward compatibility
        client_email = request.form.get('client_email') or request.form.get('client_id')
        index_name = request.form.get('index_name', 'default')
        update_mode = request.form.get('update_mode', 'update')  # 'update' or 'new'
        embed_model = request.form.get('embed_model', DEFAULT_EMBED_MODEL)

        # If not in form data, try JSON data
        if not client_email:
            try:
                json_data = request.get_json() or {}
                client_email = json_data.get('client_email') or json_data.get('client_id')
                index_name = json_data.get('index_name', index_name)
                update_mode = json_data.get('update_mode', update_mode)
                embed_model = json_data.get('embed_model', embed_model)
                print("🔍 DEBUG: Using JSON data for parameters")
            except:
                print("🔍 DEBUG: No valid JSON data found")

        # Debug parameter extraction
        print(f"🔍 DEBUG: Extracted parameters:")
        print(f"   - client_email: '{client_email}'")
        print(f"   - index_name: '{index_name}'")
        print(f"   - update_mode: '{update_mode}'")
        print(f"   - embed_model: '{embed_model}'")

        # Validate required parameters
        if not client_email:
            print("❌ DEBUG: client_email is missing or empty")
            # For testing purposes, provide a more helpful error with fallback option
            print("🔧 DEBUG: Attempting to use default client email for testing")
            client_email = "<EMAIL>"  # Fallback for testing
            print(f"🔧 DEBUG: Using fallback client_email: {client_email}")

            # Still log the issue but don't fail the request
            print("⚠️  WARNING: No client_email provided, using fallback for testing")
            print(f"   - Received form data: {dict(request.form)}")
            print(f"   - Available fields: {list(request.form.keys())}")

        # Validate update mode
        if update_mode not in ['update', 'new']:
            response, status_code = ResponseFormatter.error_response(
                error="Invalid update mode specified",
                error_type="validation_error",
                details={"valid_modes": ["update", "new"], "received": update_mode}
            )
            return jsonify(response), status_code

        # Log processing information
        print(f"📊 Processing Excel upload for client: {client_email}")
        print(f"📁 File: {file.filename}")
        print(f"🏷️  Index name: {index_name}")
        print(f"🔄 Update mode: {update_mode}")
        print(f"🧠 Embedding model: {embed_model}")

        # Save uploaded file temporarily
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
            file.save(temp_file.name)
            temp_file_path = temp_file.name

        try:
            # Validate Excel file
            is_valid, validation_message, excel_df = validate_excel_file(temp_file_path)
            if not is_valid:
                response, status_code = ResponseFormatter.error_response(
                    error=validation_message,
                    error_type="validation_error",
                    details={"filename": file.filename}
                )
                return jsonify(response), status_code

            # Enhanced language detection for CSV/Excel data
            detected_language = 'English'  # Default
            language_confidence = 0.0
            language_metadata = {}

            try:
                # Try enhanced language detection first
                from services.language_utils import enhanced_language_detector
                detected_language, language_confidence, language_metadata = enhanced_language_detector.detect_csv_language(excel_df)
                print(f"🔍 ENHANCED CSV LANGUAGE DETECTION: {detected_language} (confidence: {language_confidence:.3f})")

                # Log detailed language statistics
                if language_metadata.get('language_scores'):
                    print(f"📊 Language scores: {language_metadata['language_scores']}")

            except ImportError:
                # Fallback to basic language detection
                from services.excel_processor import detect_language_from_excel_data
                detected_language = detect_language_from_excel_data(excel_df)
                print(f"🔍 BASIC CSV LANGUAGE DETECTION: {detected_language}")

            # Determine appropriate index based on detected language
            original_index_name = index_name
            if detected_language in ['Tamil', 'Telugu', 'Kannada', 'Oriya'] and index_name == 'default':
                # For South Indian languages, use language-specific routing
                if detected_language == 'Tamil':
                    index_name = 'default'  # Tamil uses default index
                elif detected_language == 'Telugu':
                    index_name = 'default-telugu'  # Telugu gets its own index
                elif detected_language == 'Kannada':
                    index_name = 'default-kannada'  # Kannada gets its own index
                elif detected_language == 'Oriya':
                    index_name = 'default-oriya'  # Oriya gets its own index

                print(f"🌏 {detected_language} content detected, routing to index: {index_name}")
            elif detected_language != 'English' and index_name == 'default':
                # For other non-English languages, create language-specific index
                language_suffix = detected_language.lower().replace(' ', '-')
                index_name = f"default-{language_suffix}"
                print(f"🌏 {detected_language} content detected, routing to index: {index_name}")
            else:
                print(f"🌏 Using specified index '{index_name}' for {detected_language} content")

            # Create or access FAISS index
            index_result = create_faiss_index(index_name, embed_model=embed_model)
            if not index_result["success"]:
                response, status_code = ResponseFormatter.error_response(
                    error=index_result.get("error", "Failed to create FAISS index"),
                    error_type=index_result.get("error_type", "index_creation_error"),
                    details={"index_name": index_name, "embedding_model": embed_model},
                    status_code=500
                )
                return jsonify(response), status_code

            # Load existing FAISS index and metadata
            faiss_index, existing_metadata, load_success = load_faiss_index(index_name)
            if not load_success:
                response, status_code = ResponseFormatter.error_response(
                    error="Failed to load FAISS index",
                    error_type="faiss_access_error",
                    status_code=500
                )
                return jsonify(response), status_code

            # Check for duplicates if in update mode
            if update_mode == 'update':
                is_duplicate = check_duplicate_excel_upload(
                    client_email, file.filename, index_name, existing_metadata
                )
                if is_duplicate:
                    response, status_code = ResponseFormatter.error_response(
                        error=f"Excel file '{file.filename}' has already been uploaded by client '{client_email}'",
                        error_type="duplicate_upload",
                        details={
                            "filename": file.filename,
                            "client_email": client_email,
                            "suggestion": "Use update_mode='new' to force re-upload"
                        }
                    )
                    return jsonify(response), status_code

            # Get embedder
            embedder = get_embedder(embed_model)

            # Process Excel data with language information
            vectors, metadata = process_excel_data(
                excel_df, client_email, file.filename, index_name, embedder
            )

            # Enhance metadata with language information
            for meta_item in metadata:
                meta_item['detected_language'] = detected_language
                meta_item['language_confidence'] = language_confidence
                meta_item['language_detection_method'] = 'enhanced' if 'enhanced_language_detector' in locals() else 'basic'
                if language_metadata:
                    meta_item['language_metadata'] = language_metadata

            if not vectors:
                response, status_code = ResponseFormatter.error_response(
                    error="No valid data found in Excel file to process",
                    error_type="processing_error"
                )
                return jsonify(response), status_code

            # Prepare vectors for FAISS
            vectors_array = np.vstack(vectors).astype('float32')
            faiss.normalize_L2(vectors_array)

            # Handle update modes
            if update_mode == 'new':
                # Reset index and metadata
                dimension = vectors_array.shape[1]
                faiss_index = faiss.IndexFlatIP(dimension)
                combined_metadata = metadata
                print(f"🔄 Created new index with {len(vectors)} vectors")
            else:
                # Add to existing index
                combined_metadata = existing_metadata + metadata
                print(f"🔄 Adding {len(vectors)} vectors to existing index")

            # Add vectors to index
            faiss_index.add(vectors_array)

            # Save updated index and metadata
            index_dir = os.path.join(FAISS_DATA_DIR, index_name)
            os.makedirs(index_dir, exist_ok=True)

            faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
            metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

            # Save FAISS index
            faiss.write_index(faiss_index, faiss_file_path)

            # Save metadata
            with open(metadata_file_path, 'w', encoding='utf-8') as f:
                json.dump(combined_metadata, f, ensure_ascii=False, indent=2)

            print(f"✅ Successfully saved Excel data to FAISS index: {index_name}")

            # Record Excel upload in database
            try:
                from database import record_excel_upload
                columns_info = json.dumps(list(excel_df.columns))
                embedding_dimension = len(vectors[0]) if vectors else 0

                record_success, record_message = record_excel_upload(
                    file.filename,
                    index_name,
                    client_email,
                    columns_info,
                    len(excel_df),
                    embed_model,
                    embedding_dimension,
                    detected_language,
                    len(metadata)
                )

                if record_success:
                    print(f"📝 Database record created: {record_message}")
                else:
                    print(f"⚠️ Database record failed: {record_message}")

            except Exception as db_error:
                print(f"⚠️ Database recording error: {db_error}")

            # Prepare success response
            upload_data = {
                "index_name": index_name,
                "filename": file.filename,
                "client_email": client_email,
                "vector_count": len(vectors),
                "total_rows_processed": len(excel_df),
                "embedding_model": embed_model,
                "detected_language": detected_language,
                "update_mode": update_mode,
                "chunks_created": len(metadata)
            }

            success_message = f"Successfully processed Excel file '{file.filename}' with {len(vectors)} vectors"

            metadata_info = {
                "processing_stats": {
                    "rows_processed": len(excel_df),
                    "chunks_created": len(metadata),
                    "vectors_generated": len(vectors)
                },
                "file_info": {
                    "original_filename": file.filename,
                    "detected_language": detected_language,
                    "language_confidence": language_confidence,
                    "language_detection_method": 'enhanced' if 'enhanced_language_detector' in locals() else 'basic',
                    "columns": list(excel_df.columns)
                },
                "language_info": {
                    "detected_language": detected_language,
                    "confidence_score": language_confidence,
                    "detection_method": 'enhanced' if 'enhanced_language_detector' in locals() else 'basic',
                    "language_metadata": language_metadata if language_metadata else None,
                    "index_routing": {
                        "original_index": request.form.get('index_name', 'default'),
                        "final_index": index_name,
                        "routing_applied": index_name != request.form.get('index_name', 'default')
                    }
                },
                "storage_info": {
                    "faiss_index_location": f"{FAISS_DATA_DIR}/{index_name}",
                    "index_type": "Tamil-specific" if index_name.endswith('-tamil') else "Default"
                }
            }

            response, status_code = ResponseFormatter.success_response(
                data=upload_data,
                message=success_message,
                metadata=metadata_info
            )

            return jsonify(response), status_code

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass

    except Exception as e:
        # Handle unexpected errors
        response, status_code = ResponseFormatter.error_response(
            error=str(e),
            error_type="unexpected_error",
            details={
                "endpoint": "/api/upload-excel",
                "file_name": request.files.get('file', {}).filename if 'file' in request.files else None
            },
            status_code=500
        )
        return jsonify(response), status_code

@app.route('/api/list-excel-files', methods=['GET', 'POST'])
def list_excel_files():
    """
    Endpoint to list uploaded Excel files.
    """
    try:
        from database import list_excel_files as db_list_excel_files

        # Get client_email from request (GET params or POST body) - support both client_email and client_id
        client_email = None
        if request.method == 'GET':
            client_email = request.args.get('client_email') or request.args.get('client_id')
        else:  # POST
            data = request.get_json() or {}
            client_email = data.get('client_email') or data.get('client_id')

        print(f"📋 Listing Excel files for client: {client_email or 'ALL'}")

        # Get Excel files from database
        success, message, excel_files = db_list_excel_files(client_email)

        if not success:
            response, status_code = ResponseFormatter.error_response(
                error=message,
                error_type="database_error"
            )
            return jsonify(response), status_code

        # Prepare response data
        response_data = {
            "excel_files": excel_files,
            "total_count": len(excel_files),
            "client_email": client_email
        }

        metadata_info = {
            "query_info": {
                "client_filter": client_email or "None (showing all)",
                "total_files": len(excel_files)
            }
        }

        response, status_code = ResponseFormatter.success_response(
            data=response_data,
            message=f"Retrieved {len(excel_files)} Excel files",
            metadata=metadata_info
        )

        return jsonify(response), status_code

    except Exception as e:
        response, status_code = ResponseFormatter.error_response(
            error=str(e),
            error_type="unexpected_error",
            details={"endpoint": "/api/list-excel-files"},
            status_code=500
        )
        return jsonify(response), status_code

@app.route('/api/check-index', methods=['POST'])
def check_index():
    """
    Endpoint to check if a FAISS index exists.
    """
    try:
        # Get request data
        data = request.get_json()

        # Validate required parameters
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        index_name = data.get('index_name')
        client = data.get('client')
        embed_model = data.get('embed_model', DEFAULT_EMBED_MODEL)

        if not index_name:
            return jsonify({"success": False, "error": "Index name is required"}), 400

        if client:
            print(f"Processing request for client: {client}")
        print(f"Using embedding model: {embed_model}")

        # Check if FAISS index exists
        try:
            index_dir = os.path.join(FAISS_DATA_DIR, index_name)
            faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
            metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

            exists = os.path.exists(faiss_file_path) and os.path.exists(metadata_file_path)

            if exists:
                print(f"FAISS index '{index_name}' already exists. Using existing index...")
            else:
                print(f"FAISS index '{index_name}' does not exist. Will need to create it.")

            return jsonify({
                "success": True,
                "exists": exists,
                "index_name": index_name,
                "embedding_model": embed_model
            })

        except Exception as e:
            error_message = str(e)
            print(f"Error checking if FAISS index exists: {error_message}")
            return jsonify({
                "success": False,
                "error": f"Failed to check if index exists: {error_message}"
            }), 500

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/list-indexes', methods=['POST'])
def list_indexes():
    """
    Endpoint to list all Pinecone indexes in the user's account.
    This endpoint is disabled as per user request.
    """
    return jsonify({
        "success": False,
        "error": "This functionality has been disabled."
    }), 404

@app.route('/api/delete-index', methods=['POST'])
def delete_index():
    """
    Endpoint to delete a Pinecone index.
    This endpoint is disabled as per user request.
    """
    return jsonify({
        "success": False,
        "error": "This functionality has been disabled."
    }), 404

@app.route('/api/delete-faiss-index', methods=['POST'])
def delete_faiss_index():
    """
    Endpoint to delete a FAISS index directory and all its files.
    This will permanently remove the index folder and all associated files.
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No JSON data provided"
            }), 400

        index_name = data.get('index_name')

        if not index_name:
            return jsonify({
                "success": False,
                "error": "index_name is required"
            }), 400

        # Validate index name to prevent directory traversal attacks
        if not re.match(r'^[a-zA-Z0-9_-]+$', index_name):
            return jsonify({
                "success": False,
                "error": "Invalid index name. Only alphanumeric characters, underscores, and hyphens are allowed."
            }), 400

        # Prevent deletion of default index for safety
        if index_name.lower() == 'default':
            return jsonify({
                "success": False,
                "error": "Cannot delete the default index for safety reasons."
            }), 403

        # Construct the index directory path
        index_dir = os.path.join(FAISS_DATA_DIR, index_name)

        # Check if the index directory exists
        if not os.path.exists(index_dir):
            return jsonify({
                "success": False,
                "error": f"FAISS index '{index_name}' does not exist."
            }), 404

        # Check if it's actually a directory
        if not os.path.isdir(index_dir):
            return jsonify({
                "success": False,
                "error": f"'{index_name}' is not a valid index directory."
            }), 400

        # List files that will be deleted (for logging)
        files_to_delete = []
        for root, dirs, files in os.walk(index_dir):
            for file in files:
                files_to_delete.append(os.path.join(root, file))

        print(f"Attempting to delete FAISS index '{index_name}' with {len(files_to_delete)} files...")

        # Delete the entire directory and its contents
        import shutil
        shutil.rmtree(index_dir)

        print(f"Successfully deleted FAISS index directory: {index_dir}")

        return jsonify({
            "success": True,
            "message": f"Successfully deleted FAISS index '{index_name}' and all associated files.",
            "deleted_files_count": len(files_to_delete),
            "index_name": index_name
        })

    except PermissionError as e:
        error_message = f"Permission denied while deleting FAISS index: {str(e)}"
        print(error_message)
        return jsonify({
            "success": False,
            "error": error_message
        }), 403

    except FileNotFoundError as e:
        error_message = f"FAISS index files not found: {str(e)}"
        print(error_message)
        return jsonify({
            "success": False,
            "error": error_message
        }), 404

    except Exception as e:
        error_message = f"Error deleting FAISS index: {str(e)}"
        print(error_message)
        return jsonify({
            "success": False,
            "error": error_message
        }), 500

@app.route('/api/list-embedding-models', methods=['GET'])
def list_embedding_models():
    """
    Endpoint to list all available embedding models.
    """
    try:
        return jsonify({
            "success": True,
            "models": EMBEDDING_MODELS,
            "default_model": DEFAULT_EMBED_MODEL
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/list-csv-files', methods=['POST'])
def list_csv_files():
    """
    Endpoint to list all CSV files stored in the database.
    """
    try:
        # Get request data
        data = request.get_json()

        # Get client email filter if provided
        client_email = None
        if data and 'client_email' in data:
            client_email = data.get('client_email')

        # Query the database
        success, message, result = database.list_csv_files(client_email)

        if success:
            return jsonify({
                "success": True,
                "message": message,
                "files": result
            })
        else:
            return jsonify({
                "success": False,
                "error": message
            }), 500

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/get-csv-data', methods=['POST'])
def get_csv_data():
    """
    Endpoint to retrieve CSV data from the database.
    """
    try:
        # Get request data
        data = request.get_json()

        # Validate required parameters
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        index_name = data.get('index_name')
        limit = data.get('limit', 100)
        offset = data.get('offset', 0)

        if not index_name:
            return jsonify({"success": False, "error": "Index name is required"}), 400

        # Query the database
        success, message, result = database.query_csv_data(index_name, limit, offset)

        if success:
            return jsonify({
                "success": True,
                "message": message,
                "data": result["data"],
                "columns": result["columns"],
                "total_count": result["total_count"],
                "limit": result["limit"],
                "offset": result["offset"]
            })
        else:
            return jsonify({
                "success": False,
                "error": message
            }), 404

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/upload-status', methods=['POST'])
def upload_status():
    """
    Endpoint to check the status of an ongoing upload.
    """
    try:
        # Get request data
        data = request.get_json()

        # Validate required parameters
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        upload_id = data.get('upload_id')

        if not upload_id:
            return jsonify({"success": False, "error": "Upload ID is required"}), 400

        # Check if upload exists
        with active_uploads_lock:
            if upload_id not in active_uploads:
                return jsonify({
                    "success": False,
                    "error": "Upload not found or already completed",
                    "upload_id": upload_id
                }), 404

            # Return the status
            status_data = {
                "success": True,
                "upload_id": upload_id,
                "status": active_uploads[upload_id]["status"],
                "total_rows": active_uploads[upload_id]["total_rows"],
                "processed_rows": active_uploads[upload_id]["processed_rows"],
                "total_vectors": active_uploads[upload_id]["total_vectors"],
                "index_name": active_uploads[upload_id]["index_name"],
                "cancelled": active_uploads[upload_id]["cancelled"]
            }

            # Include processing time if available
            if "processing_time" in active_uploads[upload_id]:
                status_data["processing_time"] = active_uploads[upload_id]["processing_time"]

            return jsonify(status_data)

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/cancel-upload', methods=['POST'])
def cancel_upload():
    """
    Endpoint to cancel an ongoing upload with enhanced error handling
    """
    try:
        # Get request data
        data = request.get_json()

        # Validate required parameters
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        upload_id = data.get('upload_id')

        if not upload_id:
            return jsonify({"success": False, "error": "Upload ID is required"}), 400

        with active_uploads_lock:
            # Validate upload exists and isn't already completed
            if upload_id not in active_uploads:
                return jsonify({
                    "success": False,
                    "error": "Upload not found or already completed",
                    "upload_id": upload_id
                }), 404

            # Prevent cancelling already completed/cancelled uploads
            if active_uploads[upload_id]["status"] in ["complete", "cancelled"]:
                return jsonify({
                    "success": False,
                    "error": "Upload has already completed",
                    "upload_id": upload_id
                }), 409

            # Mark as cancelled and record cancellation time
            active_uploads[upload_id]["cancelled"] = True
            active_uploads[upload_id]["cancel_time"] = time.time()
            active_uploads[upload_id]["status"] = "cancelling"

            # Calculate time since start for diagnostics
            time_since_start = time.time() - active_uploads[upload_id]["start_time"]
            print(f"Cancellation requested for upload {upload_id} after {time_since_start:.2f} seconds")

            return jsonify({
                "success": True,
                "message": "Upload cancellation initiated",
                "upload_id": upload_id,
                "processed_vectors": active_uploads[upload_id]["total_vectors"],
                "processed_rows": active_uploads[upload_id]["processed_rows"]
            })

    except Exception as e:
        error_message = f"Failed to cancel upload: {str(e)}"
        print(error_message)
        return jsonify({
            "success": False,
            "error": error_message,
            "upload_id": upload_id
        }), 500



# app.py explore backend
@app.route('/api/news')
def get_financial_news():
    # Get category from query parameters, default to 'all'
    category = request.args.get('category', 'all').lower()

    # Define category-specific search queries
    category_queries = {
        'business': 'business OR corporate OR company OR companies OR profit OR revenue OR CEO',
        'markets': 'stock OR market OR nifty OR sensex OR BSE OR NSE OR trading OR shares OR equity',
        'economy': 'economy OR GDP OR inflation OR RBI OR "reserve bank" OR fiscal OR monetary OR budget',
        'technology': 'technology OR tech OR digital OR AI OR artificial intelligence OR startup OR innovation',
        'banking': 'banking OR bank OR loan OR credit OR deposit OR finance OR NBFC OR fintech',
        'policy': 'policy OR regulation OR government OR tax OR GST OR compliance OR legal',
        'all': 'finance OR stock OR market OR economy OR investment OR banking OR nifty OR sensex OR rbi OR "reserve bank" OR rupee'
    }

    # Define category-specific keywords for better categorization
    category_keywords = {
        'business': ['business', 'corporate', 'company', 'companies', 'profit', 'revenue', 'ceo', 'earnings', 'quarterly', 'annual report'],
        'markets': ['stock', 'market', 'nifty', 'sensex', 'bse', 'nse', 'trading', 'shares', 'equity', 'bull', 'bear', 'rally', 'correction'],
        'economy': ['economy', 'gdp', 'inflation', 'rbi', 'reserve bank', 'fiscal', 'monetary', 'budget', 'growth', 'recession', 'economic'],
        'technology': ['tech', 'technology', 'digital', 'ai', 'artificial intelligence', 'startup', 'innovation', 'software', 'hardware', 'app'],
        'banking': ['bank', 'banking', 'loan', 'credit', 'deposit', 'finance', 'nbfc', 'fintech', 'interest rate', 'mortgage', 'lending'],
        'policy': ['policy', 'regulation', 'government', 'tax', 'gst', 'compliance', 'legal', 'law', 'bill', 'parliament', 'ministry']
    }

    # If category is not 'all', use a more specific query to get better results
    if category != 'all':
        # Use the category-specific query
        query = category_queries[category]

        # Add more specific keywords to improve relevance
        params = {
            'apiKey': NEWS_API_KEY,
            'q': query,
            'pageSize': 100,
            'language': 'en',
            'sortBy': 'publishedAt',
            'domains': 'economictimes.indiatimes.com,moneycontrol.com,livemint.com,business-standard.com,financialexpress.com,ndtv.com/business,cnbctv18.com'
        }
    else:
        # For 'all' category, use the general query
        params = {
            'apiKey': NEWS_API_KEY,
            'q': category_queries['all'],
            'pageSize': 100,
            'language': 'en',
            'sortBy': 'publishedAt',
            'domains': 'economictimes.indiatimes.com,moneycontrol.com,livemint.com,business-standard.com,financialexpress.com,ndtv.com/business,cnbctv18.com'
        }

    print(f"Fetching news for category: {category} with query: {params['q']}")
    response = requests.get("https://newsapi.org/v2/everything", params=params)
    data = response.json()
    financial_news = []

    # Track how many articles we've assigned to each category
    category_counts = {cat: 0 for cat in category_queries.keys()}

    if data.get('status') == 'ok':
        print(f"Total Indian financial articles received: {len(data.get('articles', []))}")

        for article in data.get('articles', []):
            # Only include articles with images
            if not article.get('urlToImage'):
                continue

            # Get article content
            title = article.get('title', '').lower()
            description = article.get('description', '').lower()
            content = title + ' ' + description

            # Determine the article category based on keyword matching
            article_category = 'all'  # Default category
            max_matches = 0

            # For each category, count how many keywords match
            for cat, keywords in category_keywords.items():
                matches = sum(1 for keyword in keywords if keyword in content)
                if matches > max_matches:
                    max_matches = matches
                    article_category = cat

            # If we're fetching a specific category, ensure articles match that category
            if category != 'all':
                # If we're fetching a specific category, force that category
                article_category = category

            # Increment the count for this category
            category_counts[article_category] += 1

            item = {
                'title': article.get('title'),
                'summary': article.get('description'),
                'url': article.get('url'),
                'image_url': article.get('urlToImage'),
                'published': article.get('publishedAt'),
                'source': article.get('source', {}).get('name'),
                'category': article_category
            }

            # Format date if it exists
            if item['published']:
                try:
                    dt = datetime.fromisoformat(item['published'].replace('Z', '+00:00'))
                    item['formatted_date'] = dt.strftime('%B %d, %Y')
                except:
                    item['formatted_date'] = item['published']

            financial_news.append(item)

    # Print category distribution
    print(f"Category distribution: {category_counts}")

    # If we're fetching 'all' categories, ensure we have a good mix
    if category == 'all':
        # Sort by category to group articles
        financial_news.sort(key=lambda x: x['category'])

        # Take up to 2 articles from each category to ensure diversity
        balanced_news = []
        for cat in category_queries.keys():
            if cat == 'all':
                continue  # Skip the 'all' category itself

            cat_news = [news for news in financial_news if news['category'] == cat]
            balanced_news.extend(cat_news[:2])

        # If we still need more articles to reach 10, add more from any category
        if len(balanced_news) < 10:
            remaining_news = [news for news in financial_news if news not in balanced_news]
            balanced_news.extend(remaining_news[:10 - len(balanced_news)])

        financial_news = balanced_news[:10]
    else:
        # For specific categories, just take the top 10
        financial_news = financial_news[:10]

    # Return the list of categories along with the news
    categories = list(category_queries.keys())
    return jsonify({
        'news': financial_news,
        'categories': categories,
        'current_category': category
    })


#suggest.py /financial_querry endpoint


embedder = HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")

# Initialize DeepSeek client only if API key is available
deepseek_client = None
if DEEPSEEK_API_KEY:
    try:
        deepseek_client = OpenAI(api_key=DEEPSEEK_API_KEY, base_url="https://api.deepseek.com")
        print("[SUCCESS] DeepSeek API client initialized successfully")
    except Exception as e:
        print(f"[ERROR] Failed to initialize DeepSeek client: {e}")
        deepseek_client = None
else:
    print("[WARNING] DEEPSEEK_API_KEY not found - related questions will use fallback responses")





def generate_response(query, context_docs, selected_language="English"):
    # Handle FAISS (dict) format
    context_texts = []
    source_info = []

    for doc in context_docs:
        # FAISS format
        metadata = doc.get('metadata', {})
        text = metadata.get('chunk_text', '')
        source_type = metadata.get('source_type', 'unknown')
        url = metadata.get('url', '')
        title = metadata.get('title', 'Unknown')

        context_texts.append(text)

        # Collect unique source information
        source_entry = {
            'type': source_type,
            'url': url,
            'title': title
        }
        if source_entry not in source_info:
            source_info.append(source_entry)

    context = "\n\n".join(context_texts)

    # Create source attribution text
    source_attribution = ""
    if source_info:
        source_attribution = "\n\nSOURCES USED:\n"
        for i, source in enumerate(source_info, 1):
            source_type_display = {
                'article': 'Article',
                'youtube': 'YouTube Video',
                'pdf': 'PDF Document',
                'document': 'Document',
                'audio': 'Audio File'
            }.get(source['type'], source['type'].title())

            source_attribution += f"{i}. {source_type_display}: {source['title']}\n"
            if source['url'] and not source['url'].startswith('file://'):
                source_attribution += f"   URL: {source['url']}\n"

    # Create language-specific system prompt and user prompt
    if selected_language == "Tamil":
        system_prompt = """நீங்கள் ஒரு உதவிகரமான உதவியாளர். கேள்விகளுக்கு பதிலளிக்கும் போது, உங்கள் பதிலில் நீங்கள் பயன்படுத்தும் ஆதாரங்களை குறிப்பிடுங்கள் (எ.கா., 'கட்டுரையின் படி...', 'யூடியூப் வீடியோவின் அடிப்படையில்...', 'ஆவணத்தில் குறிப்பிட்டுள்ளபடி...').

முக்கியம்: 
- உங்கள் பதில் முழுவதும் தமிழில் மட்டுமே இருக்க வேண்டும். ஆங்கிலத்தில் பதில் கொடுக்க வேண்டாம்.
- முழுமையான மற்றும் விரிவான பதில் அளிக்கவும். அரை குறையான அல்லது முழுமையற்ற பதில்களை தவிர்க்கவும்.
- எண்களால் பட்டியலிடும் போது, ஒவ்வொரு புள்ளிக்கும் முழுமையான விளக்கம் கொடுக்கவும்.
- வார்த்தைகளை அல்லது வாக்கியங்களை மீண்டும் மீண்டும் சொல்ல வேண்டாம்."""

        user_prompt = f"""கொடுக்கப்பட்ட தகவலின் அடிப்படையில் பின்வரும் கேள்விக்கு பதிலளியுங்கள். உங்கள் பதிலில் ஆதாரங்களை குறிப்பிடுவதை உறுதி செய்யுங்கள்.

சூழல்:
{context}
{source_attribution}

கேள்வி: {query}

குறிப்பு: உங்கள் பதில் முழுவதும் தமிழில் மட்டுமே இருக்க வேண்டும்."""

    elif selected_language == "Telugu":
        system_prompt = """మీరు ఒక సహాయకరమైన సహాయకుడు. ప్రశ్నలకు సమాధానం ఇచ్చేటప్పుడు, మీ సమాధానంలో మీరు ఉపయోగించే మూలాలను పేర్కొనండి (ఉదా., 'వ్యాసం ప్రకారం...', 'యూట్యూబ్ వీడియో ఆధారంగా...', 'పత్రంలో పేర్కొన్న విధంగా...').

ముఖ్యమైనది:
- మీ సమాధానం పూర్తిగా తెలుగులో మాత్రమే ఉండాలి. ఇంగ్లీష్‌లో సమాధానం ఇవ్వకండి.
- పూర్తి మరియు వివరణాత్మక సమాధానం ఇవ్వండి. అసంపూర్ణ లేదా అర్ధ సమాధానాలను నివారించండి.
- సంఖ్యలతో జాబితా చేసేటప్పుడు, ప్రతి పాయింట్‌కు పూర్తి వివరణ ఇవ్వండి.
- పదాలను లేదా వాక్యాలను పునరావృతం చేయకండి."""

        user_prompt = f"""ఇవ్వబడిన సమాచారం ఆధారంగా కింది ప్రశ్నకు సమాధానం ఇవ్వండి. మీ సమాధానంలో మూలాలను పేర్కొనడం నిర్ధారించుకోండి.

సందర్భం:
{context}
{source_attribution}

ప్రశ్న: {query}

గమనిక: మీ సమాధానం పూర్తిగా తెలుగులో మాత్రమే ఉండాలి."""

    elif selected_language == "Kannada":
        system_prompt = """ನೀವು ಒಬ್ಬ ಸಹಾಯಕ ಸಹಾಯಕರು. ಪ್ರಶ್ನೆಗಳಿಗೆ ಉತ್ತರಿಸುವಾಗ, ನಿಮ್ಮ ಉತ್ತರದಲ್ಲಿ ನೀವು ಬಳಸುವ ಮೂಲಗಳನ್ನು ಉಲ್ಲೇಖಿಸಿ (ಉದಾ., 'ಲೇಖನದ ಪ್ರಕಾರ...', 'ಯೂಟ್ಯೂಬ್ ವೀಡಿಯೊದ ಆಧಾರದ ಮೇಲೆ...', 'ದಾಖಲೆಯಲ್ಲಿ ಹೇಳಿದಂತೆ...').

ಮುಖ್ಯ:
- ನಿಮ್ಮ ಉತ್ತರ ಸಂಪೂರ್ಣವಾಗಿ ಕನ್ನಡದಲ್ಲಿ ಮಾತ್ರ ಇರಬೇಕು. ಇಂಗ್ಲಿಷ್‌ನಲ್ಲಿ ಉತ್ತರ ಕೊಡಬೇಡಿ.
- ಸಂಪೂರ್ಣ ಮತ್ತು ವಿವರವಾದ ಉತ್ತರ ನೀಡಿ. ಅಪೂರ್ಣ ಅಥವಾ ಅರ್ಧ ಉತ್ತರಗಳನ್ನು ತಪ್ಪಿಸಿ.
- ಸಂಖ್ಯೆಗಳಿಂದ ಪಟ್ಟಿ ಮಾಡುವಾಗ, ಪ್ರತಿ ಅಂಶಕ್ಕೆ ಸಂಪೂರ್ಣ ವಿವರಣೆ ನೀಡಿ.
- ಪದಗಳನ್ನು ಅಥವಾ ವಾಕ್ಯಗಳನ್ನು ಪುನರಾವರ್ತಿಸಬೇಡಿ."""

        user_prompt = f"""ಕೊಟ್ಟಿರುವ ಮಾಹಿತಿಯ ಆಧಾರದ ಮೇಲೆ ಈ ಕೆಳಗಿನ ಪ್ರಶ್ನೆಗೆ ಉತ್ತರಿಸಿ. ನಿಮ್ಮ ಉತ್ತರದಲ್ಲಿ ಮೂಲಗಳನ್ನು ಉಲ್ಲೇಖಿಸುವುದನ್ನು ಖಚಿತಪಡಿಸಿಕೊಳ್ಳಿ.

ಸಂದರ್ಭ:
{context}
{source_attribution}

ಪ್ರಶ್ನೆ: {query}

ಗಮನಿಸಿ: ನಿಮ್ಮ ಉತ್ತರ ಸಂಪೂರ್ಣವಾಗಿ ಕನ್ನಡದಲ್ಲಿ ಮಾತ್ರ ಇರಬೇಕು."""

    elif selected_language == "Oriya":
        system_prompt = """ଆପଣ ଜଣେ ସହାୟକ ସହାୟକ। ପ୍ରଶ୍ନର ଉତ୍ତର ଦେବା ସମୟରେ, ଆପଣଙ୍କ ଉତ୍ତରରେ ଆପଣ ବ୍ୟବହାର କରୁଥିବା ଉତ୍ସଗୁଡ଼ିକୁ ଉଲ୍ଲେଖ କରନ୍ତୁ (ଯେପରି, 'ଲେଖା ଅନୁଯାୟୀ...', 'ୟୁଟ୍ୟୁବ୍ ଭିଡିଓ ଆଧାରରେ...', 'ଦଲିଲରେ ଉଲ୍ଲେଖ କରାଯାଇଥିବା ପରି...').

ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ:
- ଆପଣଙ୍କ ଉତ୍ତର ସମ୍ପୂର୍ଣ୍ଣ ଭାବରେ କେବଳ ଓଡ଼ିଆରେ ହେବା ଉଚିତ। ଇଂରାଜୀରେ ଉତ୍ତର ଦିଅନ୍ତୁ ନାହିଁ।
- ସମ୍ପୂର୍ଣ୍ଣ ଏବଂ ବିସ୍ତୃତ ଉତ୍ତର ଦିଅନ୍ତୁ। ଅସମ୍ପୂର୍ଣ୍ଣ କିମ୍ବା ଅର୍ଦ୍ଧ ଉତ୍ତରଗୁଡ଼ିକୁ ଏଡ଼ାନ୍ତୁ।
- ସଂଖ୍ୟା ସହିତ ତାଲିକା କରିବା ସମୟରେ, ପ୍ରତ୍ୟେକ ବିନ୍ଦୁ ପାଇଁ ସମ୍ପୂର୍ଣ୍ଣ ବ୍ୟାଖ୍ୟା ଦିଅନ୍ତୁ।
- ଶବ୍ଦ କିମ୍ବା ବାକ୍ୟଗୁଡ଼ିକର ପୁନରାବୃତ୍ତି କରନ୍ତୁ ନାହିଁ।"""

        user_prompt = f"""ଦିଆଯାଇଥିବା ସୂଚନା ଆଧାରରେ ନିମ୍ନଲିଖିତ ପ୍ରଶ୍ନର ଉତ୍ତର ଦିଅନ୍ତୁ। ଆପଣଙ୍କ ଉତ୍ତରରେ ଉତ୍ସଗୁଡ଼ିକୁ ଉଲ୍ଲେଖ କରିବା ନିଶ୍ଚିତ କରନ୍ତୁ।

ପ୍ରସଙ୍ଗ:
{context}
{source_attribution}

ପ୍ରଶ୍ନ: {query}

ଧ୍ୟାନ ଦିଅନ୍ତୁ: ଆପଣଙ୍କ ଉତ୍ତର ସମ୍ପୂର୍ଣ୍ଣ ଭାବରେ କେବଳ ଓଡ଼ିଆରେ ହେବା ଉଚିତ।"""

    else:
        system_prompt = """You are a helpful assistant. When answering questions, reference the sources you're using by mentioning them in your response (e.g., 'According to the article...', 'Based on the YouTube video...', 'As mentioned in the document...'). 

Important: 
- Provide complete and comprehensive answers. Avoid incomplete or partial responses.
- When using numbered lists, ensure each point has a complete explanation.
- Never end your response abruptly or with incomplete sentences."""

        user_prompt = f"""Answer the following question based on this information. Make sure to reference the sources in your response when appropriate.

CONTEXT:
{context}
{source_attribution}

QUESTION: {query}
"""

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]
    try:
        # Enhanced settings for better response quality, especially for regional languages
        # Significantly increased token limits to prevent response truncation
        max_tokens_setting = 4000 if selected_language in ["Tamil", "Telugu", "Kannada", "Oriya"] else 3000
        temperature_setting = 0.5 if selected_language in ["Tamil", "Telugu", "Kannada", "Oriya"] else 0.7

        print(f"🎯 AI Response Generation Settings:")
        print(f"   - Language: {selected_language}")
        print(f"   - Max Tokens: {max_tokens_setting}")
        print(f"   - Temperature: {temperature_setting}")

        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            max_tokens=max_tokens_setting,  # Significantly increased for complete responses
            temperature=temperature_setting,  # Lower temperature for more consistent regional language responses
            top_p=0.9,
            stream=False
        )
        return response.choices[0].message.content
    except Exception as e:
        # Handle DeepSeek/OpenAI errors gracefully, especially insufficient balance (402)
        error_msg = str(e)
        if '402' in error_msg or 'Insufficient Balance' in error_msg or 'Payment Required' in error_msg:
            return "[Error: The AI service is currently unavailable due to insufficient balance. Please contact the administrator or try again later.]"
        return f"[Error: Could not generate AI response. Details: {error_msg}]"

def generate_related_questions(query, answer, selected_language="English", context_data=None):
    """
    Enhanced related questions generation using DeepSeek API with rich context.

    Args:
        query: The original question
        answer: The AI-generated answer
        selected_language: The language for generating questions (English/Tamil/Telugu/Kannada/Oriya)
        context_data: Additional context from financial_query response (optional)

    Returns:
        List of related questions (up to 7 questions)
    """
    # Check if DeepSeek API client is available
    if not deepseek_client:
        print("⚠️ DeepSeek API client not available - using enhanced fallback questions")
        return get_enhanced_fallback_questions(selected_language, context_data)

    # Extract additional context if provided
    context_info = ""
    if context_data:
        # Extract relevant context from the financial query response
        retrieved_docs = context_data.get('retrieved_documents', [])
        index_used = context_data.get('index_used', 'default')
        search_engine = context_data.get('search_engine', 'FAISS')
        has_uploaded_content = context_data.get('has_uploaded_content', False)
        
        # Build context information
        context_parts = []
        
        if retrieved_docs:
            categories = list(set([doc.get('category', 'N/A') for doc in retrieved_docs[:3]]))
            if categories and categories != ['N/A']:
                context_parts.append(f"Categories: {', '.join(categories)}")
            
            dates = [doc.get('date', '') for doc in retrieved_docs[:3] if doc.get('date') and doc.get('date') != 'Unknown']
            if dates:
                context_parts.append(f"Time period: {min(dates)} to {max(dates)}")
        
        if has_uploaded_content:
            context_parts.append("Includes user-uploaded content")
        
        if index_used != 'default':
            context_parts.append(f"Data source: {index_used}")
        
        if context_parts:
            context_info = f"\nContext: {' | '.join(context_parts)}"

    # Create enhanced language-specific prompts
    if selected_language == "Tamil":
        prompt = f"""
நீங்கள் ஒரு நிதி ஆலோசகர். கொடுக்கப்பட்ட கேள்வி, பதில் மற்றும் சூழல் தகவலின் அடிப்படையில், 7 ஆழமான மற்றும் நடைமுறை சார்ந்த தொடர்ச்சி கேள்விகளை உருவாக்கவும். கேள்விகள் தமிழில் மட்டுமே இருக்க வேண்டும்.

கேள்விகள் இந்த வகைகளில் இருக்க வேண்டும்:
- விரிவான விளக்கம் கேட்கும் கேள்விகள்
- நடைமுறை பயன்பாடு பற்றிய கேள்விகள்
- எதிர்கால போக்குகள் பற்றிய கேள்விகள்
- ஆபத்துகள் மற்றும் வாய்ப்புகள் பற்றிய கேள்விகள்
- ஒப்பீட்டு பகுப்பாய்வு கேள்விகள்

கேள்வி: {query}
பதில்: {answer}{context_info}

தொடர்புடைய கேள்விகளை பட்டியலிடுங்கள்:"""
    elif selected_language == "Telugu":
        prompt = f"""
మీరు ఒక ఆర్థిక సలహాదారు. ఇవ్వబడిన ప్రశ్న, సమాధానం మరియు సందర్భ సమాచారం ఆధారంగా, 7 లోతైన మరియు ఆచరణాత్మక తదుపరి ప్రశ్నలను రూపొందించండి. ప్రశ్నలు తెలుగులో మాత్రమే ఉండాలి.

ప్రశ్న: {query}
సమాధానం: {answer}{context_info}

సంబంధిత ప్రశ్నలను జాబితా చేయండి:"""
    elif selected_language == "Kannada":
        prompt = f"""
ನೀವು ಒಬ್ಬ ಹಣಕಾಸು ಸಲಹೆಗಾರ. ಕೊಟ್ಟಿರುವ ಪ್ರಶ್ನೆ, ಉತ್ತರ ಮತ್ತು ಸಂದರ್ಭ ಮಾಹಿತಿಯ ಆಧಾರದ ಮೇಲೆ, 7 ಆಳವಾದ ಮತ್ತು ಪ್ರಾಯೋಗಿಕ ಮುಂದುವರಿಕೆ ಪ್ರಶ್ನೆಗಳನ್ನು ರಚಿಸಿ. ಪ್ರಶ್ನೆಗಳು ಕನ್ನಡದಲ್ಲಿ ಮಾತ್ರ ಇರಬೇಕು.

ಪ್ರಶ್ನೆ: {query}
ಉತ್ತರ: {answer}{context_info}

ಸಂಬಂಧಿತ ಪ್ರಶ್ನೆಗಳನ್ನು ಪಟ್ಟಿ ಮಾಡಿ:"""
    elif selected_language == "Oriya":
        prompt = f"""
ଆପଣ ଜଣେ ଆର୍ଥିକ ପରାମର୍ଶଦାତା। ଦିଆଯାଇଥିବା ପ୍ରଶ୍ନ, ଉତ୍ତର ଏବଂ ପ୍ରସଙ୍ଗ ସୂଚନା ଆଧାରରେ, 7ଟି ଗଭୀର ଏବଂ ବ୍ୟବହାରିକ ଅନୁସରଣ ପ୍ରଶ୍ନ ସୃଷ୍ଟି କରନ୍ତୁ। ପ୍ରଶ୍ନଗୁଡ଼ିକ କେବଳ ଓଡ଼ିଆରେ ହେବା ଉଚିତ।

ପ୍ରଶ୍ନଗୁଡ଼ିକ ଏହି ବର୍ଗଗୁଡ଼ିକରେ ହେବା ଉଚିତ:
- ବିସ୍ତୃତ ବ୍ୟାଖ୍ୟା ଏବଂ ସ୍ପଷ୍ଟୀକରଣ
- ବ୍ୟବହାରିକ ପ୍ରୟୋଗ ଏବଂ କାର୍ଯ୍ୟକାରିତା
- ଭବିଷ୍ୟତ ଧାରା ଏବଂ ପୂର୍ବାନୁମାନ
- ବିପଦ ମୂଲ୍ୟାଙ୍କନ ଏବଂ ସୁଯୋଗ
- ତୁଳନାତ୍ମକ ବିଶ୍ଳେଷଣ ଏବଂ ବିକଳ୍ପ

ପ୍ରଶ୍ନ: {query}
ଉତ୍ତର: {answer}{context_info}

ସମ୍ବନ୍ଧୀୟ ପ୍ରଶ୍ନଗୁଡ଼ିକ ତାଲିକାଭୁକ୍ତ କରନ୍ତୁ:"""
    else:  # English and other languages
        prompt = f"""
You are a financial advisor. Based on the following question, answer, and context information, generate 7 insightful and practical follow-up questions that would help users gain deeper understanding.

The questions should cover these categories:
- Detailed explanations and clarifications
- Practical applications and implementation
- Future trends and predictions  
- Risk assessment and opportunities
- Comparative analysis and alternatives
- Investment strategies and timing
- Market implications and broader impact

QUESTION: {query}
ANSWER: {answer}{context_info}

Please list the related questions:"""

    messages = [{"role": "user", "content": prompt}]
    try:
        print(f"🤖 Generating enhanced related questions using DeepSeek API in {selected_language}...")
        print(f"🔍 Context provided: {'Yes' if context_data else 'No'}")
        
        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            max_tokens=400,  # Increased for more detailed questions
            temperature=0.8,  # Slightly higher for more creative questions
            top_p=0.9
        )
        raw_text = response.choices[0].message.content
        print(f"✅ DeepSeek API response received: {len(raw_text)} characters")

        # Enhanced question extraction with better filtering
        lines = raw_text.strip().split('\n')
        questions = []
        
        for line in lines:
            # Remove numbering and clean up
            cleaned_line = re.sub(r"^\d+\.\s*", "", line).strip()
            cleaned_line = re.sub(r"^[-*]\s*", "", cleaned_line).strip()
            
            # Check if it's a valid question
            if (cleaned_line and 
                ('?' in cleaned_line or cleaned_line.endswith('?')) and 
                len(cleaned_line) > 15 and  # Minimum length for meaningful questions
                not cleaned_line.lower().startswith(('note:', 'disclaimer:', 'important:'))):
                
                # Ensure it ends with a question mark
                if not cleaned_line.endswith('?'):
                    cleaned_line += '?'
                
                questions.append(cleaned_line)

        # Enhanced filtering for quality questions with corruption detection
        filtered_questions = []
        for q in questions:
            # Skip generic or low-quality questions
            generic_patterns = [
                r'^what is\s+\w+\?$',
                r'^how to\s+\w+\?$',
                r'^can you\s+\w+\?$',
                r'^do you\s+\w+\?$'
            ]
            
            is_generic = any(re.match(pattern, q.lower()) for pattern in generic_patterns)
            
            if not is_generic and len(q) >= 20:  # Ensure substantial questions
                # Apply corruption detection and cleaning for each question
                # For multilingual languages, be very conservative
                if selected_language in ['Tamil', 'Telugu', 'Kannada', 'Oriya']:
                    # Only apply word repetition cleaning for multilingual languages
                    word_corrupted, word_cleaned = detect_word_repetition(q)
                    if word_corrupted:
                        print(f"🔧 Cleaned word repetition in generated question: {q[:50]}... -> {word_cleaned[:50]}...")
                        # Only add if the cleaned version is still meaningful
                        if len(word_cleaned.strip()) >= 15 and '?' in word_cleaned:
                            filtered_questions.append(word_cleaned)
                        else:
                            print(f"⚠️ Skipping corrupted question that became too short after cleaning")
                    else:
                        filtered_questions.append(q)
                else:
                    # For other languages, apply full corruption detection
                    is_corrupted, cleaned_question, corruption_details = detect_text_corruption(q)
                    if is_corrupted:
                        print(f"🔧 Cleaned corruption in generated question: {q[:50]}... -> {cleaned_question[:50]}...")
                        # Only add if the cleaned version is still meaningful
                        if len(cleaned_question.strip()) >= 15 and '?' in cleaned_question:
                            filtered_questions.append(cleaned_question)
                        else:
                            print(f"⚠️ Skipping corrupted question that became too short after cleaning")
                    else:
                        filtered_questions.append(q)

        # Final validation to catch any remaining corruption patterns
        final_questions = []
        for q in filtered_questions:
            # Check for excessive repetition patterns that might have been missed
            words = q.split()
            if len(words) > 0:
                # Count word frequencies
                word_freq = {}
                for word in words:
                    word_lower = word.lower().strip('.,!?')
                    word_freq[word_lower] = word_freq.get(word_lower, 0) + 1
                
                # Check if any word appears more than 30% of the time (indicating corruption)
                max_freq = max(word_freq.values()) if word_freq else 0
                repetition_ratio = max_freq / len(words) if len(words) > 0 else 0
                
                if repetition_ratio <= 0.3:  # Allow up to 30% repetition
                    final_questions.append(q)
                else:
                    print(f"🚨 Skipping highly corrupted question with {repetition_ratio:.1%} word repetition: {q[:50]}...")
        
        if final_questions:
            print(f"✅ Generated {len(final_questions)} enhanced related questions in {selected_language}")
            return final_questions[:7]
        else:
            print(f"⚠️ No valid questions found after corruption filtering, using enhanced {selected_language} fallback")
            return get_enhanced_fallback_questions(selected_language, context_data)

    except Exception as e:
        error_msg = str(e)
        print(f"❌ Error generating related questions: {error_msg}")

        # Enhanced error handling with specific responses
        if '401' in error_msg or 'Unauthorized' in error_msg:
            print("🔑 API key appears to be invalid")
            return get_error_questions(selected_language, "api_key_invalid")
        elif '402' in error_msg or 'Insufficient Balance' in error_msg or 'Payment Required' in error_msg:
            print("💳 Insufficient balance in DeepSeek account")
            return get_error_questions(selected_language, "insufficient_balance")
        elif 'timeout' in error_msg.lower() or 'connection' in error_msg.lower():
            print("🌐 Network connectivity issue")
            return get_error_questions(selected_language, "network_error")
        else:
            print(f"🔄 Using enhanced fallback questions due to error: {error_msg}")
            return get_enhanced_fallback_questions(selected_language, context_data)

def get_fallback_questions(language):
    """Get basic fallback questions for when API is not available."""
    fallback_questions = {
        "Tamil": [
            "இந்த தகவலின் முக்கிய அம்சங்கள் என்ன?",
            "இந்த தலைப்பு பற்றி மேலும் விவரங்கள் தர முடியுமா?",
            "இந்த தகவலின் தாக்கங்கள் என்ன?",
            "இது தற்போதைய போக்குகளுடன் எவ்வாறு தொடர்புடையது?",
            "இதன் சாத்தியமான நன்மைகள் மற்றும் தீமைகள் என்ன?",
            "என்ன கூடுதல் ஆராய்ச்சி உதவியாக இருக்கும்?",
            "இந்த தகவலை நடைமுறையில் எவ்வாறு பயன்படுத்தலாம்?"
        ],
        "Telugu": [
            "ఈ సమాచారంలోని ముఖ్య అంశాలు ఏమిటి?",
            "ఈ అంశం గురించి మరిన్ని వివరాలు ఇవ్వగలరా?",
            "ఈ సమాచారం యొక్క ప్రభావాలు ఏమిటి?",
            "ఇది ప్రస్తుత ధోరణులతో ఎలా సంబంధం కలిగి ఉంది?",
            "దీని సంభావ్య ప్రయోజనాలు మరియు నష్టాలు ఏమిటి?",
            "ఏ అదనపు పరిశోధన సహాయకరంగా ఉంటుంది?",
            "ఈ సమాచారాన్ని ఆచరణలో ఎలా ఉపయోగించవచ్చు?"
        ],
        "Kannada": [
            "ಈ ಮಾಹಿತಿಯ ಮುಖ್ಯ ಅಂಶಗಳು ಯಾವುವು?",
            "ಈ ವಿಷಯದ ಬಗ್ಗೆ ಹೆಚ್ಚಿನ ವಿವರಗಳನ್ನು ನೀಡಬಹುದೇ?",
            "ಈ ಮಾಹಿತಿಯ ಪರಿಣಾಮಗಳು ಯಾವುವು?",
            "ಇದು ಪ್ರಸ್ತುತ ಪ್ರವೃತ್ತಿಗಳೊಂದಿಗೆ ಹೇಗೆ ಸಂಬಂಧಿಸಿದೆ?",
            "ಇದರ ಸಂಭಾವ್ಯ ಪ್ರಯೋಜನಗಳು ಮತ್ತು ಅನಾನುಕೂಲಗಳು ಯಾವುವು?",
            "ಯಾವ ಹೆಚ್ಚುವರಿ ಸಂಶೋಧನೆ ಸಹಾಯಕವಾಗಿರುತ್ತದೆ?",
            "ಈ ಮಾಹಿತಿಯನ್ನು ಪ್ರಾಯೋಗಿಕವಾಗಿ ಹೇಗೆ ಬಳಸಬಹುದು?"
        ],
        "Oriya": [
            "ଏହି ସୂଚନାର ମୁଖ୍ୟ ବିଷୟଗୁଡ଼ିକ କ'ଣ?",
            "ଏହି ବିଷୟ ବିଷୟରେ ଅଧିକ ବିବରଣୀ ଦେଇପାରିବେ କି?",
            "ଏହି ସୂଚନାର ପ୍ରଭାବ କ'ଣ?",
            "ଏହା ବର୍ତ୍ତମାନର ଧାରା ସହିତ କିପରି ସମ୍ବନ୍ଧିତ?",
            "ଏହାର ସମ୍ଭାବ୍ୟ ଲାଭ ଏବଂ କ୍ଷତି କ'ଣ?",
            "କେଉଁ ଅତିରିକ୍ତ ଅନୁସନ୍ଧାନ ସହାୟକ ହେବ?",
            "ଏହି ସୂଚନାକୁ ବ୍ୟବହାରିକ ଭାବରେ କିପରି ପ୍ରୟୋଗ କରାଯାଇପାରିବ?"
        ]
    }
    
    return fallback_questions.get(language, [
        "What are the key points from this information?",
        "Can you provide more details about this topic?",
        "What are the implications of this information?",
        "How does this relate to current trends?",
        "What are the potential benefits and drawbacks?",
        "What additional research would be helpful?",
        "How can this information be applied practically?"
    ])

def get_enhanced_fallback_questions(language, context_data=None):
    """Get enhanced fallback questions with context awareness - dynamically generated based on context."""
    
    # Base question templates for different categories
    question_templates = {
        "English": {
            "details": ["Can you provide more specific details about {topic}?", "What are the key factors behind {topic}?"],
            "implications": ["What are the potential implications of {topic}?", "How might {topic} affect future trends?"],
            "practical": ["How can this information about {topic} be applied practically?", "What actionable steps can be taken regarding {topic}?"],
            "comparison": ["How does {topic} compare to similar situations?", "What alternatives exist for {topic}?"],
            "risks": ["What are the potential risks associated with {topic}?", "What should investors be cautious about with {topic}?"],
            "opportunities": ["What opportunities does {topic} present?", "How can one benefit from {topic}?"],
            "timing": ["What is the best timing for decisions related to {topic}?", "When should one act on {topic}?"]
        },
        "Tamil": {
            "details": ["{topic} பற்றி மேலும் குறிப்பிட்ட விவரங்களை வழங்க முடியுமா?", "{topic} க்கு பின்னால் உள்ள முக்கிய காரணிகள் என்ன?"],
            "implications": ["{topic} இன் சாத்தியமான தாக்கங்கள் என்ன?", "{topic} எதிர்கால போக்குகளை எவ்வாறு பாதிக்கலாம்?"],
            "practical": ["{topic} பற்றிய இந்த தகவலை நடைமுறையில் எவ்வாறு பயன்படுத்தலாம்?", "{topic} தொடர்பாக என்ன நடவடிக்கை எடுக்கலாம்?"],
            "comparison": ["{topic} ஒத்த சூழ்நிலைகளுடன் எவ்வாறு ஒப்பிடுகிறது?", "{topic} க்கு என்ன மாற்றுகள் உள்ளன?"],
            "risks": ["{topic} உடன் தொடர்புடைய சாத்தியமான அபாயங்கள் என்ன?", "{topic} குறித்து முதலீட்டாளர்கள் எதில் எச்சரிக்கையாக இருக்க வேண்டும்?"],
            "opportunities": ["{topic} என்ன வாய்ப்புகளை வழங்குகிறது?", "{topic} இலிருந்து எவ்வாறு பயனடையலாம்?"],
            "timing": ["{topic} தொடர்பான முடிவுகளுக்கு சிறந்த நேரம் எது?", "{topic} மீது எப்போது நடவடிக்கை எடுக்க வேண்டும்?"]
        },
        "Telugu": {
            "details": ["{topic} గురించి మరిన్ని నిర్దిష్ట వివరాలు అందించగలరా?", "{topic} వెనుక ఉన్న ముఖ్య కారకాలు ఏమిటి?"],
            "implications": ["{topic} యొక్క సంభావ్య ప్రభావాలు ఏమిటి?", "{topic} భవిష్యత్ ధోరణులను ఎలా ప్రభావితం చేయవచ్చు?"],
            "practical": ["{topic} గురించిన ఈ సమాచారాన్ని ఆచరణలో ఎలా ఉపయోగించవచ్చు?", "{topic} సంబంధించి ఏ చర్యలు తీసుకోవచ్చు?"],
            "comparison": ["{topic} ఇలాంటి పరిస్థితులతో ఎలా పోల్చవచ్చు?", "{topic} కు ఏ ప్రత్యామ్నాయాలు ఉన్నాయి?"],
            "risks": ["{topic} తో సంబంధం ఉన్న సంభావ్య ప్రమాదాలు ఏమిటి?", "{topic} గురించి పెట్టుబడిదారులు దేనిపై జాగ్రత్తగా ఉండాలి?"],
            "opportunities": ["{topic} ఏ అవకాశాలను అందిస్తుంది?", "{topic} నుండి ఎలా ప్రయోజనం పొందవచ్చు?"],
            "timing": ["{topic} సంబంధిత నిర్ణయాలకు ఉత్తమ సమయం ఎప్పుడు?", "{topic} పై ఎప్పుడు చర్య తీసుకోవాలి?"]
        },
        "Kannada": {
            "details": ["{topic} ಬಗ್ಗೆ ಹೆಚ್ಚು ನಿರ್ದಿಷ್ಟ ವಿವರಗಳನ್ನು ನೀಡಬಹುದೇ?", "{topic} ಹಿಂದಿನ ಮುಖ್ಯ ಅಂಶಗಳು ಯಾವುವು?"],
            "implications": ["{topic} ಯ ಸಂಭಾವ್ಯ ಪರಿಣಾಮಗಳು ಯಾವುವು?", "{topic} ಭವಿಷ್ಯದ ಪ್ರವೃತ್ತಿಗಳನ್ನು ಹೇಗೆ ಪ್ರಭಾವಿಸಬಹುದು?"],
            "practical": ["{topic} ಬಗ್ಗೆ ಈ ಮಾಹಿತಿಯನ್ನು ಪ್ರಾಯೋಗಿಕವಾಗಿ ಹೇಗೆ ಬಳಸಬಹುದು?", "{topic} ಸಂಬಂಧಿಸಿದಂತೆ ಯಾವ ಕ್ರಮಗಳನ್ನು ತೆಗೆದುಕೊಳ್ಳಬಹುದು?"],
            "comparison": ["{topic} ಇದೇ ರೀತಿಯ ಪರಿಸ್ಥಿತಿಗಳೊಂದಿಗೆ ಹೇಗೆ ಹೋಲಿಸುತ್ತದೆ?", "{topic} ಗೆ ಯಾವ ಪರ್ಯಾಯಗಳಿವೆ?"],
            "risks": ["{topic} ಜೊತೆ ಸಂಬಂಧಿಸಿದ ಸಂಭಾವ್ಯ ಅಪಾಯಗಳು ಯಾವುವು?", "{topic} ಬಗ್ಗೆ ಹೂಡಿಕೆದಾರರು ಯಾವುದರ ಬಗ್ಗೆ ಎಚ್ಚರಿಕೆ ವಹಿಸಬೇಕು?"],
            "opportunities": ["{topic} ಯಾವ ಅವಕಾಶಗಳನ್ನು ಒದಗಿಸುತ್ತದೆ?", "{topic} ನಿಂದ ಹೇಗೆ ಪ್ರಯೋಜನ ಪಡೆಯಬಹುದು?"],
            "timing": ["{topic} ಸಂಬಂಧಿತ ನಿರ್ಣಯಗಳಿಗೆ ಉತ್ತಮ ಸಮಯ ಯಾವಾಗ?", "{topic} ಮೇಲೆ ಯಾವಾಗ ಕ್ರಮ ತೆಗೆದುಕೊಳ್ಳಬೇಕು?"]
        },
        "Oriya": {
            "details": ["{topic} ବିଷୟରେ ଅଧିକ ନିର୍ଦ୍ଦିଷ୍ଟ ବିବରଣୀ ଦେଇପାରିବେ କି?", "{topic} ପଛରେ ଥିବା ମୁଖ୍ୟ କାରଣଗୁଡ଼ିକ କ'ଣ?"],
            "implications": ["{topic} ର ସମ୍ଭାବ୍ୟ ପ୍ରଭାବ କ'ଣ?", "{topic} ଭବିଷ୍ୟତର ଧାରାକୁ କିପରି ପ୍ରଭାବିତ କରିପାରେ?"],
            "practical": ["{topic} ବିଷୟରେ ଏହି ସୂଚନାକୁ ବ୍ୟବହାରିକ ଭାବରେ କିପରି ପ୍ରୟୋଗ କରାଯାଇପାରିବ?", "{topic} ସମ୍ବନ୍ଧରେ କେଉଁ ପଦକ୍ଷେପ ନିଆଯାଇପାରିବ?"],
            "comparison": ["{topic} ସମାନ ପରିସ୍ଥିତି ସହିତ କିପରି ତୁଳନା କରେ?", "{topic} ପାଇଁ କେଉଁ ବିକଳ୍ପ ଅଛି?"],
            "risks": ["{topic} ସହିତ ଜଡ଼ିତ ସମ୍ଭାବ୍ୟ ବିପଦ କ'ଣ?", "{topic} ବିଷୟରେ ନିବେଶକମାନେ କ'ଣ ବିଷୟରେ ସତର୍କ ରହିବା ଉଚିତ?"],
            "opportunities": ["{topic} କେଉଁ ସୁଯୋଗ ପ୍ରଦାନ କରେ?", "{topic} ରୁ କିପରି ଲାଭବାନ ହୋଇପାରିବେ?"],
            "timing": ["{topic} ସମ୍ବନ୍ଧୀୟ ନିଷ୍ପତ୍ତି ପାଇଁ ସର୍ବୋତ୍ତମ ସମୟ କେବେ?", "{topic} ଉପରେ କେବେ କାର୍ଯ୍ୟ କରିବା ଉଚିତ?"]
        }
    }
    
    # Extract topic from context or use generic term
    topic = "this topic"
    if context_data:
        retrieved_docs = context_data.get('retrieved_documents', [])
        if retrieved_docs:
            # Try to extract a topic from the first document's category or text
            first_doc = retrieved_docs[0]
            if 'category' in first_doc and first_doc['category']:
                topic = first_doc['category'].lower()
            elif 'text' in first_doc:
                # Extract potential topic from text (first few words)
                text_words = first_doc['text'].split()[:3]
                if text_words:
                    topic = ' '.join(text_words).lower()
    
    # Get templates for the specified language
    templates = question_templates.get(language, question_templates["English"])
    
    # Generate questions from different categories
    generated_questions = []
    categories = ["details", "implications", "practical", "comparison", "risks", "opportunities", "timing"]
    
    for category in categories:
        if len(generated_questions) >= 7:
            break
        if category in templates:
            template = templates[category][0]  # Use first template from each category
            question = template.format(topic=topic)
            generated_questions.append(question)
    
    # Add context-specific questions if available
    if context_data:
        has_uploaded_content = context_data.get('has_uploaded_content', False)
        index_used = context_data.get('index_used', 'default')
        
        if has_uploaded_content and len(generated_questions) < 7:
            if language == "Tamil":
                generated_questions.append("பதிவேற்றப்பட்ட உள்ளடக்கத்தில் இருந்து என்ன கூடுதல் நுண்ணறிவுகள் கிடைக்கின்றன?")
            elif language == "Telugu":
                generated_questions.append("అప్‌లోడ్ చేసిన కంటెంట్ నుండి ఏ అదనపు అంతర్దృష్టులు లభిస్తాయి?")
            elif language == "Kannada":
                generated_questions.append("ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ವಿಷಯದಿಂದ ಯಾವ ಹೆಚ್ಚುವರಿ ಒಳನೋಟಗಳು ಲಭ್ಯವಿವೆ?")
            else:
                generated_questions.append("What additional insights are available from the uploaded content?")
        
        if index_used != 'default' and len(generated_questions) < 7:
            if language == "Tamil":
                generated_questions.append(f"{index_used} தரவுத்தளத்தில் இருந்து வேறு என்ன தகவல்கள் கிடைக்கின்றன?")
            elif language == "Telugu":
                generated_questions.append(f"{index_used} డేటాబేస్ నుండి ఇంకా ఏ సమాచారం అందుబాటులో ఉంది?")
            elif language == "Kannada":
                generated_questions.append(f"{index_used} ಡೇಟಾಬೇಸ್‌ನಿಂದ ಇನ್ನೇನು ಮಾಹಿತಿ ಲಭ್ಯವಿದೆ?")
            else:
                generated_questions.append(f"What other information is available from the {index_used} database?")
    
    return generated_questions[:7]

def get_error_questions(language, error_type):
    """Get error-specific questions for different error scenarios."""
    error_questions = {
        "Tamil": {
            "api_key_invalid": ["API கீ செல்லுபடியாகவில்லை - கட்டமைப்பை சரிபார்க்கவும்"],
            "insufficient_balance": ["API சேவை கிடைக்கவில்லை - போதுமான இருப்பு இல்லை"],
            "network_error": ["நெட்வொர்க் பிழை - மீண்டும் முயற்சிக்கவும்"]
        },
        "Telugu": {
            "api_key_invalid": ["API కీ చెల్లదు - కాన్ఫిగరేషన్ తనిఖీ చేయండి"],
            "insufficient_balance": ["API సేవ అందుబాటులో లేదు - తగినంత బ్యాలెన్స్ లేదు"],
            "network_error": ["నెట్‌వర్క్ లోపం - దయచేసి మళ్లీ ప్రయత్నించండి"]
        },
        "Kannada": {
            "api_key_invalid": ["API ಕೀ ಅಮಾನ್ಯವಾಗಿದೆ - ಕಾನ್ಫಿಗರೇಶನ್ ಪರಿಶೀಲಿಸಿ"],
            "insufficient_balance": ["API ಸೇವೆ ಲಭ್ಯವಿಲ್ಲ - ಸಾಕಷ್ಟು ಬ್ಯಾಲೆನ್ಸ್ ಇಲ್ಲ"],
            "network_error": ["ನೆಟ್‌ವರ್ಕ್ ದೋಷ - ದಯವಿಟ್ಟು ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ"]
        }
    }
    
    default_errors = {
        "api_key_invalid": "Invalid API key - please check configuration",
        "insufficient_balance": "API service unavailable - insufficient balance", 
        "network_error": "Network error - please try again"
    }
    
    if language in error_questions and error_type in error_questions[language]:
        return error_questions[language][error_type]
    else:
        return [default_errors.get(error_type, "Service temporarily unavailable")]

def extract_sentences(text):
    return re.split(r'(?<=[.!?])\s+', text.strip())

def enrich_ai_response_with_urls(ai_response, api_key=None, index_name=None):
    sentences = extract_sentences(ai_response)
    enriched = []

    print(f"🔍 ENRICHMENT DEBUG: Starting URL enrichment with {len(sentences)} sentences")
    print(f"   - API Key provided: {'Yes' if api_key else 'No'}")
    print(f"   - Index Name provided: {'Yes' if index_name else 'No'}")
    print(f"   - Index Name: {index_name}")
    print(f"   - Using FAISS for enrichment")

    for i, sentence in enumerate(sentences):
        if not sentence.strip():
            continue

        print(f"🔍 ENRICHMENT SENTENCE {i+1}: Processing sentence: '{sentence[:50]}...'")

        # Use FAISS for search
        print(f"   - Calling retrieve_from_faiss_query with index_name={index_name}")
        matches = retrieve_from_faiss_query(sentence, index_name or "default", k=20)

        if matches:
            # Handle FAISS (dict) format
            top_match = matches[0].get('metadata', {})
            print(f"   🔍 FAISS metadata keys: {list(top_match.keys())}")

            # Extract data based on FAISS index structure
            # source_title should be file_name
            source_title = top_match.get("file_name", top_match.get("file_uploaded", top_match.get("title", "Unknown")))

            # source_type should be vector_id
            source_type = top_match.get("vector_id", top_match.get("source_type", "unknown"))

            # Extract file_id and page information from chunk_text if available
            chunk_text = top_match.get("chunk_text", "")
            file_id = top_match.get("file_id", "Unknown")
            page_number = top_match.get("page", "Unknown")

            # Parse file_id and page from chunk_text if they're not available as separate fields
            if chunk_text and (file_id == "Unknown" or page_number == "Unknown"):
                try:
                    # Extract file_id from chunk_text pattern: "file_id: 1745737736292"
                    if "file_id:" in chunk_text and file_id == "Unknown":
                        file_id_start = chunk_text.find("file_id:") + len("file_id:")
                        file_id_end = chunk_text.find("|", file_id_start)
                        if file_id_end == -1:
                            file_id_end = chunk_text.find(" ", file_id_start)
                        if file_id_end != -1:
                            file_id = chunk_text[file_id_start:file_id_end].strip()

                    # Extract page from chunk_text pattern: "page: 3"
                    if "page:" in chunk_text and page_number == "Unknown":
                        page_start = chunk_text.find("page:") + len("page:")
                        page_end = chunk_text.find("|", page_start)
                        if page_end == -1:
                            page_end = chunk_text.find(" ", page_start)
                        if page_end != -1:
                            page_number = chunk_text[page_start:page_end].strip()

                    # Alternative: Extract page number from beginning of page_content if it starts with a number
                    if page_number == "Unknown" and page_content:
                        lines = page_content.split('\n')
                        if lines and lines[0].strip().isdigit():
                            page_number = lines[0].strip()
                            print(f"   📖 Extracted page number from content: {page_number}")

                except Exception as e:
                    print(f"   ⚠️ Error parsing chunk_text: {str(e)}")
                    # Keep the original values if parsing fails

            # For regional languages, extract additional fields from chunk_text
            page_content = ""
            if chunk_text and "page_content:" in chunk_text:
                # Extract page_content from chunk_text structure
                try:
                    page_content_start = chunk_text.find("page_content:") + len("page_content:")
                    page_content_end = chunk_text.find("|", page_content_start)
                    if page_content_end == -1:
                        page_content_end = len(chunk_text)
                    page_content = chunk_text[page_content_start:page_content_end].strip()
                except:
                    page_content = ""

            # If no page_content extracted, use chunk_text itself
            if not page_content:
                page_content = top_match.get("page_content", chunk_text[:200] if chunk_text else "")

            # Try to get URL from various possible fields
            source_url = top_match.get("url", top_match.get("file_uploaded", "N/A"))

            # Try to get summary from various possible fields
            summary_text = top_match.get("summary", top_match.get("page_content", "Unknown"))
            
            print(f"   📋 Extracted data:")
            print(f"      - source_title (file_name): {source_title}")
            print(f"      - source_type (vector_id): {source_type}")
            print(f"      - file_id: {file_id}")
            print(f"      - page_number: {page_number}")
            print(f"      - source_url: {source_url}")
            print(f"      - page_content: {page_content[:50]}..." if page_content else "      - page_content: None")
            print(f"      - summary: {summary_text[:50]}..." if summary_text != "Unknown" else f"      - summary: {summary_text}")

            # Handle case when URL is N/A - try other fields
            if source_url == "N/A" or not source_url:
                file_uploaded = top_match.get("file_uploaded")
                if file_uploaded:
                    source_url = file_uploaded
                    print(f"   🔄 URL was N/A, using file_uploaded as URL: {file_uploaded}")
                else:
                    # If no URL available, set to N/A
                    source_url = "N/A"

            # Determine source type display based on file extension or vector_id
            if source_title and source_title.endswith('.pdf'):
                source_type_display = 'PDF Document'
            elif source_title and any(ext in source_title.lower() for ext in ['.xlsx', '.xls', '.csv']):
                source_type_display = 'Excel/CSV File'
            elif 'chunk' in str(source_type).lower():
                source_type_display = 'Document Chunk'
            else:
                source_type_display = 'Document'

            # Create enhanced summary - use "Unknown" as requested if no summary available
            if summary_text and summary_text != "Unknown" and len(summary_text.strip()) > 0:
                enhanced_summary = f"Source: {summary_text[:100]}..." if len(summary_text) > 100 else f"Source: {summary_text}"
            else:
                enhanced_summary = "Source: Unknown"

            enriched.append({
                "sentence": sentence,
                "source_title": source_title,  # This will be the file_name
                "source_type": source_type,    # This will be the vector_id
                "file_id": file_id,           # File ID from FAISS data
                "page": page_number,          # Page number from FAISS data
                "page_content": page_content, # Page content from FAISS data
                "vector_id": source_type,     # Vector ID for regional languages
                "file_uploaded": source_title, # File uploaded name
                "summary": enhanced_summary,   # This will be "Source: Unknown" or actual summary
                "url": source_url             # This will be "N/A" or actual URL
            })
            print(f"   ✅ Found match for sentence {i+1}")
            print(f"      - Final source_title: {source_title}")
            print(f"      - Final source_type: {source_type}")
            print(f"      - Final file_id: {file_id}")
            print(f"      - Final page: {page_number}")
            print(f"      - Final summary: {enhanced_summary}")
            print(f"      - Final url: {source_url}")
        else:
            enriched.append({
                "sentence": sentence,
                "source_title": "Unknown",
                "source_type": "unknown",
                "file_id": "Unknown",
                "page": "Unknown",
                "page_content": "",
                "vector_id": "unknown",
                "file_uploaded": "Unknown",
                "summary": "Source: Unknown",
                "url": "N/A"
            })
            print(f"   ❌ No match found for sentence {i+1}")

    print(f"🔍 ENRICHMENT COMPLETE: Processed {len(enriched)} sentences")
    return enriched

@app.route('/financial_query', methods=['POST'])
def handle_query():
    """
    Improved Financial Query Handler with New Translation Flow:
    1. Detect query language (Kannada/Tamil/Telugu/Oriya)
    2. Search FAISS data in matching language index
    3. Get response in FAISS data language
    4. Translate final response using @vitalets/google-translate-api
    """
    data = request.get_json()
    query = data.get("query", "").strip()

    # Get client-specific configuration if provided in the request
    api_key = data.get("api_key")
    requested_index_name = data.get("index_name")  # Store the originally requested index
    client_email = data.get("client_email")

    # Get language preference and translation settings
    client_preferred_language = data.get("language", "English")  # Store client preference
    target_language = data.get("target_language")  # Language for response translation

    # Initialize selected_language to English for auto-detection
    selected_language = "English"

    # Import Google Translate API service for final response translation
    try:
        from services.google_translate_api_service_new import google_translate_service
        google_translate_available = google_translate_service.is_service_available()
        print("✅ Google Translate API service loaded")
    except ImportError:
        print("⚠️ Google Translate API service not available")
        google_translate_available = False

    # Auto-detect language from query text if not explicitly set
    def is_tamil_text(text):
        """Check if text contains Tamil characters"""
        import re
        # Tamil Unicode range: \u0B80-\u0BFF
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        return bool(tamil_pattern.search(text))
    
    def is_telugu_text(text):
        """Check if text contains Telugu characters"""
        import re
        # Telugu Unicode range: \u0C00-\u0C7F
        telugu_pattern = re.compile(r'[\u0C00-\u0C7F]')
        return bool(telugu_pattern.search(text))
    
    def is_kannada_text(text):
        """Check if text contains Kannada characters"""
        import re
        # Kannada Unicode range: \u0C80-\u0CFF
        kannada_pattern = re.compile(r'[\u0C80-\u0CFF]')
        return bool(kannada_pattern.search(text))
    
    def is_oriya_text(text):
        """Check if text contains Oriya characters"""
        import re
        # Oriya Unicode range: \u0B00-\u0B7F
        oriya_pattern = re.compile(r'[\u0B00-\u0B7F]')
        return bool(oriya_pattern.search(text))

    # Import enhanced language detection for language detection only
    try:
        from services.language_utils import enhanced_language_detector
        enhanced_detection_available = True
    except ImportError:
        print("⚠️ Enhanced language detection services not available, using basic detection")
        enhanced_detection_available = False

    # Always perform language detection to ensure accuracy
    if enhanced_detection_available:
        # Use enhanced language detection
        detected_language, confidence, scores = enhanced_language_detector.detect_language_with_confidence(query)
        if confidence >= 0.1 and detected_language in ["Tamil", "Telugu", "Kannada", "Oriya"]:
            selected_language = detected_language
            print(f"🔍 ENHANCED AUTO-DETECTED {detected_language.upper()} TEXT in query: '{query[:50]}...'")
            print(f"🌏 ENHANCED LANGUAGE AUTO-DETECTION: Switching to {detected_language} language processing (confidence: {confidence:.3f})")
        else:
            # Fallback to basic detection
            if is_tamil_text(query):
                selected_language = "Tamil"
                print(f"🔍 BASIC AUTO-DETECTED TAMIL TEXT in query: '{query[:50]}...'")
            elif is_telugu_text(query):
                selected_language = "Telugu"
                print(f"🔍 BASIC AUTO-DETECTED TELUGU TEXT in query: '{query[:50]}...'")
            elif is_kannada_text(query):
                selected_language = "Kannada"
                print(f"🔍 BASIC AUTO-DETECTED KANNADA TEXT in query: '{query[:50]}...'")
            elif is_oriya_text(query):
                selected_language = "Oriya"
                print(f"🔍 BASIC AUTO-DETECTED ORIYA TEXT in query: '{query[:50]}...'")
            else:
                # No regional language detected, default to English
                print(f"🔍 NO SOUTH INDIAN LANGUAGE DETECTED, defaulting to English")
                selected_language = "English"
    else:
        # Use basic detection
        if is_tamil_text(query):
            selected_language = "Tamil"
            print(f"🔍 AUTO-DETECTED TAMIL TEXT in query: '{query[:50]}...'")
            print(f"🌏 LANGUAGE AUTO-DETECTION: Switching to Tamil language processing")
        elif is_telugu_text(query):
            selected_language = "Telugu"
            print(f"🔍 AUTO-DETECTED TELUGU TEXT in query: '{query[:50]}...'")
            print(f"🌏 LANGUAGE AUTO-DETECTION: Switching to Telugu language processing")
        elif is_kannada_text(query):
            selected_language = "Kannada"
            print(f"🔍 AUTO-DETECTED KANNADA TEXT in query: '{query[:50]}...'")
            print(f"🌏 LANGUAGE AUTO-DETECTION: Switching to Kannada language processing")
        elif is_oriya_text(query):
            selected_language = "Oriya"
            print(f"🔍 AUTO-DETECTED ORIYA TEXT in query: '{query[:50]}...'")
            print(f"🌏 LANGUAGE AUTO-DETECTION: Switching to Oriya language processing")
        else:
            # No regional language detected, default to English
            print(f"🔍 NO REGIONAL LANGUAGE DETECTED, defaulting to English")
            selected_language = "English"

    # Store original query for the new translation flow
    original_query = query

    # Enhanced logic for regional language index processing
    # Generalized logic for Tamil, Telugu, Kannada, and Oriya
    regional_languages = ["Tamil", "Telugu", "Kannada", "Oriya"]
    regional_index_map = {
        "Tamil": "tamil",
        "Telugu": "telugu",
        "Kannada": "kannada",
        "Oriya": "oriya"
    }

    # Determine the FAISS data language based on the index being used
    data_language = "English"  # Default
    if requested_index_name and requested_index_name.lower() in regional_index_map.values():
        # Find the language for this index
        for lang, idx in regional_index_map.items():
            if idx == requested_index_name.lower():
                data_language = lang
                break

    print(f"🔍 NEW TRANSLATION FLOW:")
    print(f"   - Query Language: {selected_language}")
    print(f"   - FAISS Data Language: {data_language}")
    print(f"   - Target Response Language: {target_language or selected_language}")
    print(f"   - Index: {requested_index_name or 'default'}")

    # In the new flow, we search FAISS directly with the original query
    # No query translation needed - we search in the matching language index
    print(f"🔍 Using original query for FAISS search: '{query[:50]}...'")
    search_query = query

    # Get upload context information
    upload_context = data.get("upload_context", "")
    has_recent_uploads = data.get("has_recent_uploads", False)

    # Enhanced logging for request analysis
    print(f"🔍 FINANCIAL QUERY REQUEST ANALYSIS:")
    print(f"   - Original Query: {original_query[:50]}...")
    print(f"   - Language: {selected_language}")
    print(f"   - API Key provided: {'Yes' if api_key else 'No'}")
    print(f"   - API Key (first 10 chars): {api_key[:10] if api_key else 'None'}...")
    print(f"   - Index Name provided: {'Yes' if requested_index_name else 'No'}")
    print(f"   - Requested Index Name: {requested_index_name}")
    print(f"   - Client Email: {client_email if client_email else 'Not provided'}")

    # Initialize index_name with the requested value
    index_name = requested_index_name

    # Enhanced index selection logic based on PINE collection data
    # Priority: 1. Explicitly requested index, 2. User's available indexes from PINE, 3. Default
    if requested_index_name:
        # User explicitly requested a specific index - validate it exists for the user
        print(f"🎯 EXPLICIT INDEX REQUEST: '{requested_index_name}'")
        index_name = requested_index_name
    elif client_email:
        # Try to get user's available indexes from PINE collection
        print(f"🔍 FETCHING USER INDEXES from PINE collection for: {client_email}")
        try:
            # Fetch user's available indexes from PINE collection
            response = requests.get(
                f"https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&f1_field=client_S&f1_op=eq&f1_value={client_email}",
                headers={
                    "Content-Type": "application/json",
                    "xxxid": "PINE"
                }
            )
            
            if response.ok:
                pine_data = response.json()
                if pine_data.get("statusCode") == 200 and pine_data.get("source"):
                    # Parse PINE data to get user's available indexes
                    user_indexes = []
                    for item in pine_data["source"]:
                        try:
                            record = json.loads(item)
                            if record.get("index_name") and record.get("index_name") not in user_indexes:
                                user_indexes.append(record.get("index_name"))
                        except json.JSONDecodeError:
                            continue
                    
                    if user_indexes:
                        # Use the first available index for the user
                        index_name = user_indexes[0]
                        print(f"✅ USER INDEXES FOUND: {user_indexes}")
                        print(f"🎯 USING FIRST USER INDEX: '{index_name}'")
                    else:
                        # No user-specific indexes found, use default
                        index_name = "default"
                        print(f"ℹ️ No user-specific indexes found, using default")
                else:
                    # No data found for user, use default
                    index_name = "default"
                    print(f"ℹ️ No PINE data found for user, using default")
            else:
                # API call failed, use default
                index_name = "default"
                print(f"⚠️ PINE API call failed, using default")
        except Exception as e:
            # Error occurred, use default
            index_name = "default"
            print(f"❌ Error fetching user indexes: {e}, using default")
    else:
        # No client email and no explicit index request, use default
        index_name = "default"
        print(f"🎯 NO CLIENT EMAIL OR EXPLICIT INDEX, using default")

    # Handle Tamil language routing (after index selection)
    if selected_language == "Tamil" and not requested_index_name:
        # For Tamil queries without explicit index request, prefer Tamil-specific indexes
        if client_email:
            # Check if user has Tamil-specific indexes
            try:
                response = requests.get(
                    f"https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&f1_field=client_S&f1_op=eq&f1_value={client_email}",
                    headers={
                        "Content-Type": "application/json",
                        "xxxid": "PINE"
                    }
                )
                
                if response.ok:
                    pine_data = response.json()
                    if pine_data.get("statusCode") == 200 and pine_data.get("source"):
                        # Look for Tamil-specific indexes
                        tamil_indexes = []
                        for item in pine_data["source"]:
                            try:
                                record = json.loads(item)
                                idx_name = record.get("index_name", "").lower()
                                if "tamil" in idx_name or "ta" in idx_name:
                                    tamil_indexes.append(record.get("index_name"))
                            except json.JSONDecodeError:
                                continue
                        
                        if tamil_indexes:
                            index_name = tamil_indexes[0]
                            print(f"🌏 TAMIL LANGUAGE: Found Tamil-specific index '{index_name}'")
                        else:
                            print(f"🌏 TAMIL LANGUAGE: No Tamil-specific index found, using '{index_name}'")
            except Exception as e:
                print(f"⚠️ Error checking for Tamil indexes: {e}")
        else:
            print(f"🌏 TAMIL LANGUAGE: No client email, using current index '{index_name}'")

    # Log the request details and fetch API key if needed
    if api_key and index_name:
        print(f"✅ USING CLIENT-SPECIFIC CONFIGURATION:")
        print(f"   - Target Index: {index_name}")
        print(f"   - API Key: {api_key[:10]}...")
    elif client_email and not api_key:
        # Fetch API key from PINE collection if not provided
        print(f"🔄 FETCHING API KEY FROM PINE COLLECTION:")
        print(f"   - Client Email: {client_email}")
        try:
            # Make a request to the PINE collection API to get API key
            response = requests.get(
                f"https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&f1_field=client_S&f1_op=eq&f1_value={client_email}",
                headers={
                    "Content-Type": "application/json",
                    "xxxid": "PINE"
                }
            )

            if response.ok:
                pine_data = response.json()
                if pine_data.get("statusCode") == 200 and pine_data.get("source"):
                    # Parse each item in the source array (they are JSON strings)
                    for item in pine_data["source"]:
                        try:
                            record = json.loads(item)
                            if record.get("api_key"):
                                api_key = record.get("api_key")
                                print(f"✅ Found API key for user: {client_email}")
                                break
                        except json.JSONDecodeError:
                            continue
                    
                    if not api_key:
                        print(f"⚠️ No API key found for user: {client_email}")
                else:
                    print(f"ℹ️ No PINE data found for user: {client_email}")
        except Exception as e:
            print(f"❌ Error fetching API key from PINE collection: {e}")
    else:
        print("Using provided configuration or defaults")

    if not query:
        return jsonify({"error": "Query is required."}), 400

    # Initialize variables for the new simplified flow
    early_optimization_detected = False
    skip_translation = False

    # 🚀 EARLY OPTIMIZATION CHECK: If we have a regional language query with matching index name,
    # we can predict that optimization will be applied and skip translation immediately
    if (selected_language in regional_languages and
        requested_index_name and
        requested_index_name.lower() == regional_index_map.get(selected_language, "").lower() and
        (not target_language or target_language == selected_language)):

        early_optimization_detected = True
        skip_translation = True
        search_query = original_query  # Use original query for better semantic matching
        print(f"🚀 EARLY OPTIMIZATION DETECTED: {selected_language} query with {selected_language} index")
        print(f"🚀 SKIPPING ALL TRANSLATION - Using original query for maximum performance")
    else:
        # Use original query for search in new simplified flow (no query translation needed)
        search_query = original_query
        print(f"🔍 Using original query for search: '{search_query[:50]}...'")
        print(f"🔍 Searching in {data_language} index with {selected_language} query")

    # Final logging of what will be used (after search_query is defined)
    print(f"🔧 FINAL CONFIGURATION:")
    print(f"   - Index to use: {index_name}")
    print(f"   - API Key available: {'Yes' if api_key else 'No'}")
    print(f"   - Client Email: {client_email}")
    print(f"   - Query Language: {selected_language}")
    print(f"   - Original Query: {original_query[:50]}...")
    print(f"   - Search Query: {search_query[:50]}...")

    # Use FAISS for document retrieval
    matches = []

    # Use default index if no index is specified, considering language preference
    if not index_name:
        if selected_language == "Tamil":
            index_name = "default"
            print("🎯 No specific index provided, using DEFAULT TAMIL FAISS index")
        else:
            index_name = "default"
            print("🎯 No specific index provided, using DEFAULT FAISS index")

    if index_name:
        print(f"🎯 USING FAISS INDEX: {index_name}")

        # Validate user access to the index if client email is provided
        # Default index is accessible to all users, only validate access for other indexes
        if client_email and index_name != 'default':
            try:
                # Import the filter service
                from pine_filter_service import PineFilterService

                # Check if user has access to this index
                has_access, access_message = PineFilterService.validate_user_access(client_email, index_name)

                if not has_access:
                    error_msg = f"Access denied to index '{index_name}'. {access_message}"
                    print(f"❌ {error_msg}")
                    return jsonify({
                        "error": error_msg,
                        "error_type": "access_denied",
                        "user_email": client_email,
                        "index_name": index_name
                    }), 403

                print(f"✅ User {client_email} has access to index '{index_name}'")

            except Exception as e:
                print(f"⚠️ Error validating user access: {e}")
                # Continue without access validation if there's an error
        elif index_name == 'default':
            print(f"✅ User {client_email or 'anonymous'} accessing default index (allowed for all users)")
        else:
            print(f"⚠️ No client email provided for access validation")

        # Try FAISS, prioritizing uploaded content if recent uploads exist
        # Increased k value for better sentence matching and response quality
        matches = retrieve_from_faiss_query(search_query, index_name, k=25, prioritize_uploads=has_recent_uploads)

    if not matches:
        error_msg = f"No matching documents found in FAISS index"
        if index_name:
            error_msg += f" '{index_name}'"
        error_msg += ". Please try a different query or select another index."

        return jsonify({
            "error": error_msg
        }), 404

    retrieved_docs = []
    for i, match in enumerate(matches):
        # Handle FAISS (dict) format
        metadata = match.get('metadata', {})
        score = round(match.get('score', 0) * 100, 2)

        retrieved_docs.append({
            "rank": i + 1,
            "score": f"{score}%",
            "date": metadata.get('record_date', metadata.get('upload_timestamp', 'Unknown')),
            "category": metadata.get('category', 'N/A'),
            "text": metadata.get('chunk_text', 'No text')
        })

    # Generate AI response in the FAISS data language
    # This ensures the response is generated in the same language as the data
    ai_response = generate_response(search_query, matches, data_language)
    
    # 🔍 DEBUG: Check raw AI response for corruption (skip for direct regional language processing)
    print(f"🔍 RAW AI RESPONSE (before any processing): {ai_response[:100]}{'...' if len(ai_response) > 100 else ''}")
    
    # Skip corruption detection for direct regional language processing to prevent false positives
    if skip_translation and selected_language in ["Tamil", "Telugu", "Kannada", "Oriya"]:
        print(f"🌏 SKIPPING CORRUPTION DETECTION for direct {selected_language} processing to preserve response quality")
    elif len(ai_response) > 10:
        # Use enhanced script-aware character diversity calculation
        char_diversity = calculate_script_aware_char_diversity(ai_response)
        print(f"🔍 Raw AI response diversity analysis: {char_diversity['diversity_ratio']:.3f} (normalized: {char_diversity['normalized_diversity']:.3f})")
        
        # Use enhanced script-aware corruption detection
        is_corrupted, cleaned_response, corruption_details = detect_text_corruption(ai_response, char_diversity)
        if is_corrupted:
            print("❌ CORRUPTION DETECTED in raw AI response!")
            print(f"🔧 Using cleaned response instead of corrupted original")
            print(f"🔍 Corruption confidence: {corruption_details['confidence']:.3f}")
            ai_response = cleaned_response  # Use cleaned version
        else:
            print("✅ Raw AI response appears clean")
    
    enriched_sentences = enrich_ai_response_with_urls(ai_response, api_key=api_key, index_name=index_name)

    # Analyze data sources in retrieved documents first
    upload_sources = []
    has_uploaded_content = False

    for doc in retrieved_docs:
        # Check if this document came from uploaded content
        if any(match.get('metadata', {}).get('upload_source') for match in matches
               if match.get('metadata', {}).get('chunk_text') == doc.get('text')):
            has_uploaded_content = True
            # Extract source information
            for match in matches:
                meta = match.get('metadata', {})
                if meta.get('chunk_text') == doc.get('text') and meta.get('upload_source'):
                    source_info = meta.get('url', meta.get('title', 'Unknown source'))
                    if source_info not in upload_sources:
                        upload_sources.append(source_info)

    # Data language is already determined earlier based on index name
    # Update data language detection based on index name if not already set correctly
    if index_name and index_name.lower() in ["tamil", "telugu", "kannada", "oriya"]:
        data_language = index_name.capitalize()
        print(f"🔍 Data language confirmed from index: {data_language}")
    else:
        print(f"🔍 Data language confirmed: {data_language}")

    # Initialize response language (will be updated based on processing flow)
    response_language = target_language or selected_language

    # 🚀 ENHANCED OPTIMIZATION: Check if query language matches data language for direct processing
    # This optimization significantly reduces response time by avoiding unnecessary translation
    if (early_optimization_detected or 
        (selected_language in regional_languages and 
         data_language == selected_language and 
         (not target_language or target_language == selected_language))):
        
        if early_optimization_detected:
            print(f"🚀 EARLY OPTIMIZATION CONFIRMED: {selected_language} query with {selected_language} index")
        else:
            print(f"🚀 DATA-BASED OPTIMIZATION ACTIVATED: Query language ({selected_language}) matches data language ({data_language})")
        
        print(f"🚀 SKIPPING ALL TRANSLATION SERVICES for maximum performance")
        
        # Override previous translation decisions for optimal performance
        skip_translation = True
        
        # Use original query for better semantic matching (if not already set)
        if not early_optimization_detected:
            search_query = original_query
        
        # Set response language to match query/data language
        response_language = selected_language
        
        # Mark this as optimized direct processing
        optimized_direct_processing = True
        
        print(f"🚀 PERFORMANCE BOOST: Direct {selected_language} processing - no translation overhead")
        print(f"🚀 BENEFITS: Faster response time, reduced server load, better semantic matching")
    else:
        optimized_direct_processing = False

    # Generate enhanced related questions with context
    context_for_questions = {
        'retrieved_documents': retrieved_docs,
        'index_used': index_name,
        'search_engine': "FAISS",
        'has_uploaded_content': has_uploaded_content,
        'upload_sources': upload_sources,
        'query_language': selected_language,
        'response_language': response_language,
        'data_language': data_language
    }
    
    related_questions = generate_related_questions(search_query, ai_response, response_language, context_for_questions)

    # Generate multilingual reference labels
    def get_multilingual_labels(language):
        """Generate reference labels in the specified language"""
        labels = {
            'English': {
                'source': 'Source',
                'references': 'References',
                'page': 'Page',
                'document': 'Document',
                'summary': 'Summary'
            },
            'Tamil': {
                'source': 'மூலம்',
                'references': 'குறிப்புகள்',
                'page': 'பக்கம்',
                'document': 'ஆவணம்',
                'summary': 'சுருக்கம்'
            },
            'Telugu': {
                'source': 'మూలం',
                'references': 'సూచనలు',
                'page': 'పేజీ',
                'document': 'పత్రం',
                'summary': 'సారాంశం'
            },
            'Kannada': {
                'source': 'ಮೂಲ',
                'references': 'ಉಲ್ಲೇಖಗಳು',
                'page': 'ಪುಟ',
                'document': 'ದಾಖಲೆ',
                'summary': 'ಸಾರಾಂಶ'
            },
            'Oriya': {
                'source': 'ଉତ୍ସ',
                'references': 'ସନ୍ଦର୍ଭ',
                'page': 'ପୃଷ୍ଠା',
                'document': 'ଦଲିଲ',
                'summary': 'ସାରାଂଶ'
            },
            'Odia': {
                'source': 'ଉତ୍ସ',
                'references': 'ସନ୍ଦର୍ଭ',
                'page': 'ପୃଷ୍ଠା',
                'document': 'ଦଲିଲ',
                'summary': 'ସାରାଂଶ'
            }
        }
        return labels.get(language, labels['English'])

    # Get appropriate labels for the data language
    reference_labels = get_multilingual_labels(data_language)

    # Prepare the initial response in FAISS data language
    response_data = {
        "query": original_query,  # Always return the original query
        "retrieved_documents": retrieved_docs,
        "ai_response": ai_response,
        "sentence_analysis": enriched_sentences,
        "related_questions": related_questions,
        "reference_labels": reference_labels,
        "api_environment": API_ENVIRONMENT,
        "index_used": index_name if index_name else "default",
        "search_engine": "FAISS",
        "has_uploaded_content": has_uploaded_content,
        "upload_sources": upload_sources,
        "prioritized_uploads": has_recent_uploads,
        "query_language": selected_language,
        "faiss_data_language": data_language,
        "regional_language_detected": selected_language in ["Tamil", "Telugu", "Kannada", "Oriya"],
        "translation_flow": "improved_google_translate_api",
        "optimized_direct_processing": optimized_direct_processing,  # New flag for enhanced optimization
        "performance_optimization_applied": optimized_direct_processing,  # User-friendly flag
        "early_optimization_detected": early_optimization_detected,  # Flag for early optimization
        "optimization_type": "early" if early_optimization_detected else ("data_based" if optimized_direct_processing else "none")
    }

    # NEW TRANSLATION FLOW: Apply Google Translate API for final response translation
    final_response_language = target_language or selected_language

    print(f"🌐 NEW TRANSLATION FLOW:")
    print(f"   - Query Language: {selected_language}")
    print(f"   - FAISS Data Language: {data_language}")
    print(f"   - Target Response Language: {final_response_language}")

    # Check if translation is needed
    # For regional language queries, if AI already responded in the same language, inject references but don't translate
    if final_response_language in ["Tamil", "Telugu", "Kannada", "Oriya"]:
        # Check if AI response is already in the target language by detecting language
        ai_response_text = response_data.get('ai_response', '')
        if ai_response_text:
            # Simple check: if response contains regional language characters, it's likely already in that language
            is_already_in_target_language = False
            if final_response_language == "Tamil" and any('\u0B80' <= char <= '\u0BFF' for char in ai_response_text):
                is_already_in_target_language = True
            elif final_response_language == "Telugu" and any('\u0C00' <= char <= '\u0C7F' for char in ai_response_text):
                is_already_in_target_language = True
            elif final_response_language == "Kannada" and any('\u0C80' <= char <= '\u0CFF' for char in ai_response_text):
                is_already_in_target_language = True
            elif final_response_language == "Oriya" and any('\u0B00' <= char <= '\u0B7F' for char in ai_response_text):
                is_already_in_target_language = True

            if is_already_in_target_language:
                print(f"🎯 AI response already in {final_response_language}, injecting reference numbers only")
                needs_translation = True  # We still need to call translation service for reference injection
                inject_references_only = True
            else:
                needs_translation = (
                    final_response_language != data_language and
                    google_translate_available
                )
                inject_references_only = False
        else:
            needs_translation = (
                final_response_language != data_language and
                google_translate_available
            )
            inject_references_only = False
    else:
        needs_translation = (
            final_response_language != data_language and
            final_response_language in ["Tamil", "Telugu", "Kannada", "Oriya"] and
            google_translate_available
        )
        inject_references_only = False

    # Initialize inject_references_only if not set
    if 'inject_references_only' not in locals():
        inject_references_only = False

    if needs_translation:
        if inject_references_only:
            print(f"🔢 Injecting reference numbers into {final_response_language} response")
        else:
            print(f"🔄 Translating response from {data_language} to {final_response_language} using Google Translate API")

        try:
            # Use the Google Translate API service to translate the entire response
            translated_response = google_translate_service.translate_financial_response(
                response_data,
                final_response_language
            )

            if translated_response and translated_response.get('success'):
                # Update the response with translated content
                response_data = translated_response.get('data', translated_response.get('response', response_data))

                if inject_references_only:
                    print(f"✅ Successfully injected reference numbers into {final_response_language} response")
                else:
                    print(f"✅ Successfully translated response to {final_response_language}")

                response_data['translation_applied'] = True
                response_data['translation_method'] = 'google_translate_api'
                response_data['reference_numbers_injected'] = True

                # Check if this was a fallback response (translation failed but references injected)
                if translated_response.get('data', {}).get('query_metadata', {}).get('translation_fallback'):
                    response_data['translation_fallback'] = True
                    response_data['translation_error'] = translated_response.get('data', {}).get('query_metadata', {}).get('translation_error', 'Translation failed')
                    print(f"⚠️ Translation failed but reference numbers injected successfully")

            else:
                print(f"⚠️ Translation/reference injection failed, keeping original response")
                response_data['translation_applied'] = False
                response_data['translation_error'] = translated_response.get('error', 'Unknown error')

        except Exception as e:
            print(f"❌ Error during translation/reference injection: {str(e)}")
            response_data['translation_applied'] = False
            response_data['translation_error'] = str(e)
    else:
        print(f"✅ No translation needed - response already in target language ({final_response_language})")
        response_data['translation_applied'] = False
        response_data['translation_reason'] = 'same_language' if final_response_language == data_language else 'not_supported'

    # Return the final response (translation already applied if needed)
    print(f"🎯 Final response language: {final_response_language}")
    print(f"✅ Response preparation complete")

    # Add final metadata to response
    response_data['final_response_language'] = final_response_language
    response_data['processing_flow'] = 'improved_translation_flow'

    return jsonify(response_data)









@app.route('/api/list-categories', methods=['POST'])
def list_categories():
    """
    Endpoint to list all categories (FAISS indexes) for UI display with enhanced email filtering.
    """
    try:
        # Get request data
        data = request.get_json()

        # Get email filter if provided
        email = None
        if data and 'email' in data:
            email = data.get('email')

        # Query the database with enhanced filtering
        success, message, result = database.list_pine_categories(email)

        if success:
            return jsonify({
                "success": True,
                "message": message,
                "categories": result,
                "filtered_by_email": email is not None,
                "user_email": email if email else None
            })
        else:
            return jsonify({
                "success": False,
                "error": message
            }), 500

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/user-indices', methods=['POST'])
def get_user_indices():
    """
    Endpoint to get all PINE collection indices for a specific user.
    Ensures data isolation by returning only indices belonging to the authenticated user.
    """
    try:
        # Get request data
        data = request.get_json()

        if not data or 'email' not in data:
            return jsonify({
                "success": False,
                "error": "Email is required"
            }), 400

        email = data.get('email')
        print(f"🔍 Getting indices for user: {email}")

        # Import the filter service
        from pine_filter_service import PineFilterService

        # Get user's indices with enhanced filtering
        success, message, indices = PineFilterService.get_user_indices(email)

        print(f"📊 Filter service result: success={success}, indices={indices}")

        if success:
            return jsonify({
                "success": True,
                "message": message,
                "indices": indices,
                "count": len(indices),
                "user_email": email
            })
        else:
            return jsonify({
                "success": False,
                "error": message,
                "indices": [],
                "count": 0,
                "user_email": email
            }), 200  # Return 200 instead of 400 for better debugging

    except Exception as e:
        print(f"❌ Error in get_user_indices: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/validate-user-access', methods=['POST'])
def validate_user_access():
    """
    Endpoint to validate if a user has access to a specific index.
    """
    try:
        # Get request data
        data = request.get_json()

        if not data or 'email' not in data or 'index_name' not in data:
            return jsonify({
                "success": False,
                "error": "Email and index_name are required"
            }), 400

        email = data.get('email')
        index_name = data.get('index_name')

        # Import the filter service
        from pine_filter_service import PineFilterService

        # Validate user access
        has_access, message = PineFilterService.validate_user_access(email, index_name)

        return jsonify({
            "success": True,
            "has_access": has_access,
            "message": message,
            "user_email": email,
            "index_name": index_name
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/debug-pine-collection', methods=['GET'])
def debug_pine_collection():
    """
    Debug endpoint to see what's in the PINE collection database.
    """
    try:
        conn = database.get_connection()
        cursor = conn.cursor()

        # Get all records from pine_collection
        cursor.execute("SELECT id, index_name, email, upload_date FROM pine_collection ORDER BY upload_date DESC")
        rows = cursor.fetchall()

        records = []
        for row in rows:
            records.append({
                "id": row[0],
                "index_name": row[1],
                "email": row[2],
                "upload_date": row[3]
            })

        conn.close()

        return jsonify({
            "success": True,
            "total_records": len(records),
            "records": records
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/migrate-user-indexes', methods=['POST'])
def migrate_user_indexes():
    """
    Migration endpoint to associate existing FAISS indexes with a user's email.
    This helps populate the PINE collection for users who had indexes before the filtering system.
    """
    try:
        data = request.get_json()

        if not data or 'email' not in data:
            return jsonify({
                "success": False,
                "error": "Email is required"
            }), 400

        email = data.get('email')
        selected_indexes = data.get('indexes', [])  # Optional: specific indexes to migrate

        # Validate email
        if not email or not email.strip():
            return jsonify({
                "success": False,
                "error": "Valid email is required"
            }), 400

        # Get all available FAISS indexes
        available_indexes = []
        if os.path.exists(FAISS_DATA_DIR):
            for item in os.listdir(FAISS_DATA_DIR):
                item_path = os.path.join(FAISS_DATA_DIR, item)
                if os.path.isdir(item_path):
                    # Check if both .faiss and .json files exist
                    faiss_file = os.path.join(item_path, f"{item}.faiss")
                    json_file = os.path.join(item_path, f"{item}.json")

                    if item == "default":
                        # Check legacy naming for default
                        legacy_faiss_file = os.path.join(item_path, "default1.faiss")
                        legacy_json_file = os.path.join(item_path, "default1.json")
                        if os.path.exists(faiss_file) and os.path.exists(json_file):
                            available_indexes.append(item)
                        elif os.path.exists(legacy_faiss_file) and os.path.exists(legacy_json_file):
                            available_indexes.append(item)
                    elif item == "default":
                        # Check legacy naming for Tamil index
                        legacy_faiss_file = os.path.join(item_path, "default1.faiss")
                        legacy_json_file = os.path.join(item_path, "default1.json")
                        if os.path.exists(legacy_faiss_file) and os.path.exists(legacy_json_file):
                            available_indexes.append(item)
                    else:
                        if os.path.exists(faiss_file) and os.path.exists(json_file):
                            available_indexes.append(item)

        # If specific indexes were provided, filter to only those
        if selected_indexes:
            indexes_to_migrate = [idx for idx in selected_indexes if idx in available_indexes]
        else:
            indexes_to_migrate = available_indexes

        # Migrate indexes to PINE collection
        migrated_count = 0
        errors = []

        for index_name in indexes_to_migrate:
            try:
                success, message = database.add_pine_category(index_name, email)
                if success:
                    migrated_count += 1
                    print(f"✅ Migrated index '{index_name}' for user {email}")
                else:
                    errors.append(f"Failed to migrate {index_name}: {message}")
            except Exception as e:
                errors.append(f"Error migrating {index_name}: {str(e)}")

        return jsonify({
            "success": True,
            "message": f"Migration completed for user {email}",
            "migrated_count": migrated_count,
            "total_available": len(available_indexes),
            "migrated_indexes": indexes_to_migrate[:migrated_count],
            "errors": errors if errors else None
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

def retrieve_from_faiss_query(query: str, index_name: str, k: int = 5, prioritize_uploads: bool = False) -> List[Dict]:
    """
    Search the FAISS index with a query and return top-k matches.
    Optionally prioritize recently uploaded content.

    Args:
        query: Search query
        index_name: Name of the FAISS index
        k: Number of results to return
        prioritize_uploads: Whether to boost scores for recently uploaded content

    Returns:
        List of match dictionaries
    """
    try:
        # Load FAISS index and metadata
        faiss_index, metadata_store, success = load_faiss_index(index_name)
        if not success or faiss_index is None or not metadata_store:
            return []

        # Get embedder (use default model for now)
        embedder = get_embedder(DEFAULT_EMBED_MODEL)

        # Embed the query
        query_vector = embedder.embed_documents([query])[0]
        query_embedding = np.array([query_vector]).astype("float32")

        # Normalize for cosine similarity
        faiss.normalize_L2(query_embedding)

        # Search the index
        distances, indices = faiss_index.search(query_embedding, k)

        results = []
        for rank, idx in enumerate(indices[0]):
            if idx < len(metadata_store) and idx >= 0:
                meta = metadata_store[idx]
                base_score = float(distances[0][rank])

                # Boost score for recently uploaded content if prioritization is enabled
                if prioritize_uploads and meta.get("upload_source"):
                    # Check if this is from a recent upload (has upload_source field)
                    upload_sources = ["youtube_upload", "article_upload", "pdf_upload", "document_upload", "audio_upload"]
                    if meta.get("upload_source") in upload_sources:
                        # Boost the score by 20% for uploaded content
                        base_score = base_score * 1.2
                        print(f"📈 Boosted score for uploaded content: {meta.get('source_type', 'unknown')} - {base_score:.4f}")

                # Create a match object similar to Pinecone's structure
                match = {
                    'score': base_score,
                    'metadata': {
                        'chunk_text': meta.get("chunk_text", ""),
                        'upload_timestamp': meta.get("upload_timestamp", "Unknown"),
                        'row_idx': meta.get("row_idx", 0),
                        'chunk_idx': meta.get("chunk_idx", 0),
                        'embedding_model': meta.get("embedding_model", "Unknown")
                    }
                }
                # Add any additional metadata fields
                for key, value in meta.items():
                    if key not in match['metadata']:
                        match['metadata'][key] = value

                results.append(match)

        # Sort results by score (descending) if prioritization was applied
        if prioritize_uploads:
            results.sort(key=lambda x: x['score'], reverse=True)

        return results

    except Exception as e:
        print(f"Error retrieving from FAISS index {index_name}: {e}")
        return []

@app.route('/api/list-faiss-indexes', methods=['GET', 'POST'])
def list_faiss_indexes():
    """
    Endpoint to list available FAISS indexes, optionally filtered by user email.
    Supports both GET (all indexes) and POST (user-filtered indexes).
    Always includes 'default' as the first option if it exists and user has access.
    """
    try:
        user_email = None

        # Check if this is a POST request with user email for filtering
        if request.method == 'POST':
            data = request.get_json()
            if data and 'email' in data:
                user_email = data.get('email')
                print(f"🔍 Filtering FAISS indexes for user: {user_email}")

        # Get all available indexes from filesystem
        all_available_indexes = []
        default_index_exists = False

        # Check if FAISS data directory exists
        if os.path.exists(FAISS_DATA_DIR):
            # List all subdirectories in FAISS_DATA_DIR
            for item in os.listdir(FAISS_DATA_DIR):
                item_path = os.path.join(FAISS_DATA_DIR, item)
                if os.path.isdir(item_path):
                    # Check if both .faiss and .json files exist
                    faiss_file = os.path.join(item_path, f"{item}.faiss")
                    json_file = os.path.join(item_path, f"{item}.json")

                    # Special handling for default and Tamil indexes with legacy file names
                    if item == "default":
                        # Check standard naming first
                        if os.path.exists(faiss_file) and os.path.exists(json_file):
                            default_index_exists = True
                            all_available_indexes.append(item)
                        else:
                            # Check legacy naming
                            legacy_faiss_file = os.path.join(item_path, "default1.faiss")
                            legacy_json_file = os.path.join(item_path, "default1.json")
                            if os.path.exists(legacy_faiss_file) and os.path.exists(legacy_json_file):
                                default_index_exists = True
                                all_available_indexes.append(item)
                                print(f"Found default index with legacy file names")
                    elif item == "default":
                        # Tamil index uses legacy naming convention
                        legacy_faiss_file = os.path.join(item_path, "default1.faiss")
                        legacy_json_file = os.path.join(item_path, "default1.json")
                        if os.path.exists(legacy_faiss_file) and os.path.exists(legacy_json_file):
                            all_available_indexes.append(item)
                            print(f"Found Tamil index with legacy file names")
                    else:
                        # For other indexes, use standard naming
                        if os.path.exists(faiss_file) and os.path.exists(json_file):
                            all_available_indexes.append(item)

        # If user email is provided, filter indexes by user access
        if user_email:
            try:
                # Import the filter service
                from pine_filter_service import PineFilterService

                # Get user's accessible indexes
                success, message, user_indexes = PineFilterService.get_user_indices(user_email)

                if success:
                    # Filter filesystem indexes to only include user's accessible ones
                    user_specific_indexes = [idx for idx in all_available_indexes if idx in user_indexes]
                    print(f"📊 User {user_email} has access to {len(user_specific_indexes)} user-specific indexes: {user_specific_indexes}")

                    # Always include 'default' index for all users (even new accounts)
                    available_indexes = user_specific_indexes.copy()
                    if default_index_exists and 'default' not in available_indexes:
                        available_indexes.append('default')
                        print(f"✅ Added default index for user {user_email}")

                else:
                    print(f"❌ Failed to get user indexes: {message}")
                    # For new users or on error, provide at least the default index
                    available_indexes = ['default'] if default_index_exists else []

            except Exception as e:
                print(f"❌ Error filtering indexes by user: {e}")
                # For new users or on error, provide at least the default index
                available_indexes = ['default'] if default_index_exists else []
        else:
            # No user filtering - return all available indexes
            available_indexes = all_available_indexes

        # Always put 'default' first if it exists
        if default_index_exists and 'default' in available_indexes:
            available_indexes.remove('default')
            available_indexes.insert(0, "default")

        # Ensure we always have at least the default index if it exists
        if not available_indexes and default_index_exists:
            available_indexes = ["default"]

        print(f"📋 Final available FAISS indexes: {available_indexes}")
        print(f"🎯 Default index exists: {default_index_exists}")
        print(f"👤 User filtering: {'Yes' if user_email else 'No'}")

        return jsonify({
            "success": True,
            "indexes": available_indexes,
            "total_count": len(available_indexes),
            "default_index": "default",
            "filtered_by_user": user_email is not None,
            "user_email": user_email
        })

    except Exception as e:
        print(f"Error in list_faiss_indexes endpoint: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/query-faiss', methods=['POST'])
def query_faiss():
    """
    Endpoint to query FAISS index with a search query using professional response formatting.
    """
    try:
        # Get and validate request data
        data = request.get_json()

        if not data:
            response, status_code = ResponseFormatter.error_response(
                error="Request body is required",
                error_type="validation_error"
            )
            return jsonify(response), status_code

        # Extract and validate parameters
        query = data.get('query', '').strip()
        index_name = data.get('index_name', '').strip()
        k = data.get('k', 5)

        # Validate query
        if not query:
            response, status_code = ResponseFormatter.error_response(
                error="Search query is required",
                error_type="validation_error",
                details={"required_fields": ["query"]}
            )
            return jsonify(response), status_code

        # Apply text corruption detection and cleaning to search query
        is_query_corrupted, cleaned_query, query_corruption_details = detect_text_corruption(query)
        
        if is_query_corrupted:
            print(f"🔧 Detected and cleaned corruption in search query")
            print(f"   Original: '{query[:100]}...'")
            print(f"   Cleaned: '{cleaned_query[:100]}...'")
            print(f"   Corruption type: {query_corruption_details.get('reason', 'unknown')}")
            query = cleaned_query  # Use cleaned query for search

        # Validate index name
        if not index_name:
            response, status_code = ResponseFormatter.error_response(
                error="Index name is required",
                error_type="validation_error",
                details={"required_fields": ["index_name"]}
            )
            return jsonify(response), status_code

        # Validate user access to the index
        user_email = data.get('user_email', '').strip()
        if user_email and index_name != 'default':
            # Default index is accessible to all users, only validate access for other indexes
            try:
                # Import the filter service
                from pine_filter_service import PineFilterService

                # Check if user has access to this index
                has_access, access_message = PineFilterService.validate_user_access(user_email, index_name)

                if not has_access:
                    response, status_code = ResponseFormatter.error_response(
                        error=f"Access denied to index '{index_name}'. {access_message}",
                        error_type="access_denied",
                        details={
                            "user_email": user_email,
                            "index_name": index_name,
                            "suggestion": "Please use an index you have access to or contact an administrator"
                        }
                    )
                    return jsonify(response), status_code

                print(f"✅ User {user_email} has access to index '{index_name}'")

            except Exception as e:
                print(f"⚠️ Error validating user access: {e}")
                # Continue without access validation if there's an error
        elif index_name == 'default':
            print(f"✅ User {user_email or 'anonymous'} accessing default index (allowed for all users)")
        else:
            print(f"⚠️ No user email provided for access validation")

        # Validate k parameter
        try:
            k = int(k)
            if k <= 0 or k > 100:
                raise ValueError("k must be between 1 and 100")
        except (ValueError, TypeError):
            response, status_code = ResponseFormatter.error_response(
                error="Invalid value for 'k' parameter",
                error_type="validation_error",
                details={"valid_range": "1-100", "received": k}
            )
            return jsonify(response), status_code

        print(f"🔍 Searching FAISS index '{index_name}' for query: '{query}' (top {k} results)")

        # Search FAISS index
        results = retrieve_from_faiss_query(query, index_name, k)

        # Prepare search results data
        search_data = {
            "query": query,
            "index_name": index_name,
            "results": results,
            "total_results": len(results),
            "search_parameters": {
                "top_k": k,
                "search_type": "semantic_similarity"
            }
        }

        # Prepare metadata
        metadata = {
            "search_stats": {
                "results_found": len(results),
                "search_time": "< 1 second",  # Could be enhanced with actual timing
                "index_type": "FAISS"
            },
            "result_quality": {
                "highest_score": max([r.get('score', 0) for r in results]) if results else 0,
                "lowest_score": min([r.get('score', 0) for r in results]) if results else 0,
                "average_score": sum([r.get('score', 0) for r in results]) / len(results) if results else 0
            }
        }

        message = f"Found {len(results)} relevant results for your search query"
        if len(results) == 0:
            message = "No results found for your search query. Try using different keywords or check the index name."

        response, status_code = ResponseFormatter.success_response(
            data=search_data,
            message=message,
            metadata=metadata
        )

        return jsonify(response), status_code

    except Exception as e:
        # Handle search errors professionally
        response, status_code = ResponseFormatter.error_response(
            error=str(e),
            error_type="search_error",
            details={
                "endpoint": "/api/query-faiss",
                "query": data.get('query', 'N/A') if 'data' in locals() else 'N/A',
                "index_name": data.get('index_name', 'N/A') if 'data' in locals() else 'N/A'
            },
            status_code=500
        )
        return jsonify(response), status_code

# Enhanced upload endpoints for ChatInputUpload integration
@app.route('/api/process_youtube', methods=['POST'])
def process_youtube_enhanced():
    """Enhanced YouTube URL processing endpoint with PINE collection integration"""
    try:
        data = request.get_json()
        url = data.get('url', '').strip()
        index_name = data.get('index_name', 'default')
        client_email = data.get('client_email', '')

        if not url:
            return jsonify({'success': False, 'error': 'URL is required'})

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Import and use YouTube processor
        try:
            from services.youtube_processor import process_youtube_url
            success = process_youtube_url(url, index_name=index_name)
        except ImportError:
            return jsonify({'success': False, 'error': 'YouTube processor not available'})

        if success:
            # Store in PINE collection if client email provided
            if client_email:
                try:
                    db_success, db_message = database.add_pine_category(
                        index_name=index_name,
                        email=client_email
                    )
                    if db_success:
                        print(f"✅ Stored YouTube processing in PINE collection: {db_message}")
                except Exception as e:
                    print(f"⚠️ Failed to store in PINE collection: {e}")

            return jsonify({
                'success': True,
                'message': 'YouTube video processed successfully',
                'upload_id': upload_id,
                'index_name': index_name
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to process YouTube video'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_article', methods=['POST'])
def process_article_enhanced():
    """Enhanced Article URL processing endpoint with PINE collection integration"""
    try:
        data = request.get_json()
        url = data.get('url', '').strip()
        index_name = data.get('index_name', 'default')
        client_email = data.get('client_email', '')

        if not url:
            return jsonify({'success': False, 'error': 'URL is required'})

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Import and use Article processor
        try:
            from services.article_processor import process_article_url
            success = process_article_url(url, index_name=index_name)
        except ImportError:
            return jsonify({'success': False, 'error': 'Article processor not available'})

        if success:
            # Store in PINE collection if client email provided
            if client_email:
                try:
                    db_success, db_message = database.add_pine_category(
                        index_name=index_name,
                        email=client_email
                    )
                    if db_success:
                        print(f"✅ Stored Article processing in PINE collection: {db_message}")
                except Exception as e:
                    print(f"⚠️ Failed to store in PINE collection: {e}")

            return jsonify({
                'success': True,
                'message': 'Article processed successfully',
                'upload_id': upload_id,
                'index_name': index_name
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to process article'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_pdf', methods=['POST'])
def process_pdf_enhanced():
    """Enhanced PDF file processing endpoint with PINE collection integration - No temporary files"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No PDF file uploaded'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No PDF file selected'})

        # Get additional parameters
        index_name = request.form.get('index_name', 'default')
        client_email = request.form.get('client_email', '')

        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext != '.pdf':
            return jsonify({
                'success': False,
                'error': f'Only PDF files are supported. Got: {file_ext}'
            })

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Read file content directly into memory (no temporary file)
        print(f"📄 Reading PDF file content: {file.filename}")
        file_content = file.read()

        if not file_content:
            return jsonify({'success': False, 'error': 'Empty PDF file'})

        print(f"📊 PDF file size: {len(file_content)} bytes")

        try:
            # Import and use PDF processor with file content
            try:
                from services.pdf_processor import process_pdf_file
                success = process_pdf_file(
                    file_content=file_content,
                    original_filename=file.filename,
                    index_name=index_name
                )
            except ImportError:
                return jsonify({'success': False, 'error': 'PDF processor not available'})

            if success:
                # Store in PINE collection if client email provided
                if client_email:
                    try:
                        db_success, db_message = database.add_pine_category(
                            index_name=index_name,
                            email=client_email
                        )
                        if db_success:
                            print(f"✅ Stored PDF processing in PINE collection: {db_message}")
                    except Exception as e:
                        print(f"⚠️ Failed to store in PINE collection: {e}")

                return jsonify({
                    'success': True,
                    'message': 'PDF processed and indexed successfully (no temp files)',
                    'upload_id': upload_id,
                    'index_name': index_name,
                    'filename': file.filename
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to process PDF'})

        except Exception as processing_error:
            print(f"❌ Error during PDF processing: {processing_error}")
            return jsonify({'success': False, 'error': f'PDF processing failed: {str(processing_error)}'})

    except Exception as e:
        print(f"❌ General error in PDF endpoint: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_audio', methods=['POST'])
def process_audio_enhanced():
    """Enhanced Audio file processing endpoint with PINE collection integration"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No audio file uploaded'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No audio file selected'})

        # Get additional parameters
        index_name = request.form.get('index_name', 'default')
        client_email = request.form.get('client_email', '')

        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        supported_audio_formats = {'.mp3', '.wav', '.m4a', '.flac', '.ogg'}
        if file_ext not in supported_audio_formats:
            return jsonify({
                'success': False,
                'error': f'Unsupported audio format: {file_ext}. Supported: {list(supported_audio_formats)}'
            })

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Save uploaded file temporarily
        temp_filename = f"temp_{upload_id}_{file.filename}"
        temp_dir = os.path.join(FAISS_DATA_DIR, "temp")
        os.makedirs(temp_dir, exist_ok=True)
        temp_path = os.path.join(temp_dir, temp_filename)

        # Save the file
        file.save(temp_path)

        try:
            # Import and use Audio processor
            try:
                from services.audio_proccessor import process_audio_file
                success = process_audio_file(temp_path, original_filename=file.filename, index_name=index_name)
            except ImportError:
                return jsonify({'success': False, 'error': 'Audio processor not available'})

            if success:
                # Store in PINE collection if client email provided
                if client_email:
                    try:
                        db_success, db_message = database.add_pine_category(
                            index_name=index_name,
                            email=client_email
                        )
                        if db_success:
                            print(f"✅ Stored Audio processing in PINE collection: {db_message}")
                    except Exception as e:
                        print(f"⚠️ Failed to store in PINE collection: {e}")

                return jsonify({
                    'success': True,
                    'message': 'Audio file processed and indexed successfully',
                    'upload_id': upload_id,
                    'index_name': index_name,
                    'filename': file.filename
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to process audio file'})

        finally:
            # Clean up temporary file
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except Exception as e:
                print(f"Warning: Could not remove temporary file {temp_path}: {e}")

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/process_document', methods=['POST'])
def process_document_enhanced():
    """Enhanced Document processing endpoint with PINE collection integration"""
    try:
        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file uploaded'})

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'})

        # Get additional parameters
        index_name = request.form.get('index_name', 'default')
        client_email = request.form.get('client_email', '')

        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        supported_extensions = {'.doc', '.docx', '.txt', '.rtf'}
        if file_ext not in supported_extensions:
            return jsonify({
                'success': False,
                'error': f'Unsupported file type: {file_ext}. Supported: {list(supported_extensions)}'
            })

        # Generate unique upload ID for tracking
        upload_id = str(uuid.uuid4())

        # Save uploaded file temporarily
        temp_filename = f"temp_{upload_id}_{file.filename}"
        temp_dir = os.path.join(FAISS_DATA_DIR, "temp")
        os.makedirs(temp_dir, exist_ok=True)
        temp_path = os.path.join(temp_dir, temp_filename)

        # Save the file
        file.save(temp_path)

        try:
            # Import and use Document processor
            try:
                from services.document_processor import process_document_file
                success = process_document_file(temp_path, original_filename=file.filename, index_name=index_name)
            except ImportError:
                return jsonify({'success': False, 'error': 'Document processor not available'})

            if success:
                # Store in PINE collection if client email provided
                if client_email:
                    try:
                        db_success, db_message = database.add_pine_category(
                            index_name=index_name,
                            email=client_email
                        )
                        if db_success:
                            print(f"✅ Stored Document processing in PINE collection: {db_message}")
                    except Exception as e:
                        print(f"⚠️ Failed to store in PINE collection: {e}")

                return jsonify({
                    'success': True,
                    'message': 'Document processed and indexed successfully',
                    'upload_id': upload_id,
                    'index_name': index_name,
                    'filename': file.filename
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to process document'})

        finally:
            # Clean up temporary file
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except Exception as e:
                print(f"Warning: Could not remove temporary file {temp_path}: {e}")

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Data Management API Endpoints
@app.route('/api/get-faiss-metadata/<index_name>', methods=['GET'])
def get_faiss_metadata(index_name):
    """
    Endpoint to get metadata (JSON data) for a specific FAISS index.
    """
    try:
        print(f"📊 Fetching metadata for index: {index_name}")
        print(f"📁 FAISS_DATA_DIR: {FAISS_DATA_DIR}")

        # Load the metadata for the specified index
        index_dir = os.path.join(FAISS_DATA_DIR, index_name)
        print(f"📂 Index directory: {index_dir}")
        print(f"📂 Directory exists: {os.path.exists(index_dir)}")

        metadata_file_path = os.path.join(index_dir, f"{index_name}.json")
        print(f"📄 Metadata file path: {metadata_file_path}")
        print(f"📄 File exists: {os.path.exists(metadata_file_path)}")

        # Special handling for default index with legacy file names
        if index_name == "default" and not os.path.exists(metadata_file_path):
            legacy_metadata_file = os.path.join(index_dir, "default1.json")
            print(f"📄 Legacy metadata file path: {legacy_metadata_file}")
            print(f"📄 Legacy file exists: {os.path.exists(legacy_metadata_file)}")
            if os.path.exists(legacy_metadata_file):
                metadata_file_path = legacy_metadata_file
                print(f"Using legacy metadata file for default index")

        if not os.path.exists(metadata_file_path):
            # List directory contents for debugging
            if os.path.exists(index_dir):
                files_in_dir = os.listdir(index_dir)
                print(f"📋 Files in directory: {files_in_dir}")
            return jsonify({
                "success": False,
                "error": f"Metadata file not found for index '{index_name}'. Checked: {metadata_file_path}"
            }), 404

        # Load and return the metadata
        with open(metadata_file_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)

        # Transform metadata to include row IDs
        transformed_data = []
        for i, item in enumerate(metadata):
            row_data = {
                "id": f"row_{i}",
                **item
            }
            transformed_data.append(row_data)

        return jsonify({
            "success": True,
            "data": transformed_data,
            "total": len(transformed_data),
            "index_name": index_name
        })

    except Exception as e:
        print(f"Error fetching metadata for index {index_name}: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/delete-faiss-rows/<index_name>', methods=['DELETE'])
def delete_faiss_rows(index_name):
    """
    Endpoint to delete specific rows from a FAISS index.
    """
    try:
        data = request.get_json()
        if not data or 'row_ids' not in data:
            return jsonify({
                "success": False,
                "error": "row_ids parameter is required"
            }), 400

        row_ids = data['row_ids']
        if not isinstance(row_ids, list):
            return jsonify({
                "success": False,
                "error": "row_ids must be a list"
            }), 400

        print(f"🗑️ Deleting {len(row_ids)} rows from index: {index_name}")

        # Load the current index and metadata
        faiss_index, metadata_store, success = load_faiss_index(index_name)
        if not success:
            return jsonify({
                "success": False,
                "error": f"Failed to load index '{index_name}'"
            }), 404

        # Extract row indices from row_ids (assuming format "row_X")
        indices_to_delete = []
        for row_id in row_ids:
            if isinstance(row_id, str) and row_id.startswith("row_"):
                try:
                    index_num = int(row_id.split("_")[1])
                    if 0 <= index_num < len(metadata_store):
                        indices_to_delete.append(index_num)
                except (ValueError, IndexError):
                    continue

        if not indices_to_delete:
            return jsonify({
                "success": False,
                "error": "No valid row indices found"
            }), 400

        # Sort indices in descending order to avoid index shifting issues
        indices_to_delete.sort(reverse=True)

        # Remove metadata entries
        original_count = len(metadata_store)
        for index in indices_to_delete:
            if 0 <= index < len(metadata_store):
                del metadata_store[index]

        deleted_count = original_count - len(metadata_store)

        # Save the updated metadata (simplified approach)
        save_success = save_faiss_index(index_name, faiss_index, metadata_store)
        if not save_success:
            return jsonify({
                "success": False,
                "error": "Failed to save updated index"
            }), 500

        return jsonify({
            "success": True,
            "deleted_count": deleted_count,
            "remaining_count": len(metadata_store)
        })

    except Exception as e:
        print(f"Error deleting rows from index {index_name}: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/translation_performance', methods=['GET'])
def get_translation_performance():
    """Get translation service performance statistics"""
    try:
        from services.translation_service import translation_service
        
        performance_stats = translation_service.get_performance_stats()
        cache_stats = translation_service.get_cache_stats()
        health_status = translation_service.get_health_status()
        
        return ResponseFormatter.success_response(
            data={
                'performance': performance_stats,
                'cache': cache_stats,
                'health': health_status,
                'optimization_tips': [
                    f"Cache hit rate: {performance_stats['cache_hit_rate_percent']:.1f}% - {'Excellent' if performance_stats['cache_hit_rate_percent'] > 80 else 'Good' if performance_stats['cache_hit_rate_percent'] > 60 else 'Needs improvement'}",
                    f"Average translation time: {performance_stats['avg_translation_time_ms']:.1f}ms - {'Fast' if performance_stats['avg_translation_time_ms'] < 500 else 'Moderate' if performance_stats['avg_translation_time_ms'] < 1000 else 'Slow'}",
                    f"Total translations processed: {performance_stats['total_translations']}",
                    f"Batch translations used: {performance_stats['batch_translations']} (recommended for better performance)"
                ]
            },
            message="Translation performance statistics retrieved successfully"
        )
        
    except Exception as e:
        return ResponseFormatter.error_response(
            error=f"Failed to get translation performance: {str(e)}",
            error_type="performance_error"
        )

@app.route('/api/multilingual_financial_query', methods=['POST'])
def multilingual_financial_query():
    """
    Enhanced endpoint for Telugu/Kannada/Tamil financial queries with translation flow:
    1. Detect language of incoming query
    2. For Tamil: Route to Tamil FAISS API if available, otherwise translate
    3. For Telugu/Kannada: Translate query to English
    4. Send English query to financial_query endpoint
    5. Translate English response back to original language
    6. Return translated response
    """
    try:
        data = request.get_json() or {}
        original_query = data.get('query', '').strip()
        
        if not original_query:
            return jsonify({
                "success": False,
                "error": "Query is required"
            }), 400

        # Apply text corruption detection and cleaning to input query
        is_query_corrupted, cleaned_query, query_corruption_details = detect_text_corruption(original_query)
        
        if is_query_corrupted:
            print(f"🔧 Detected and cleaned corruption in input query")
            print(f"   Original: '{original_query[:100]}...'")
            print(f"   Cleaned: '{cleaned_query[:100]}...'")
            print(f"   Corruption type: {query_corruption_details.get('reason', 'unknown')}")
            original_query = cleaned_query  # Use cleaned query for processing

        # Import translation service
        try:
            from services.translation_service import TranslationService
            translation_service = TranslationService()
            translation_available = True
        except ImportError:
            return jsonify({
                "success": False,
                "error": "Translation service not available"
            }), 500

        # Detect the language of the query
        detected_language = translation_service.detect_language(original_query)
        print(f"🔍 Detected language: {detected_language} for query: '{original_query[:50]}...'")
        
        # Language mapping for response
        language_names = {
            'te': 'Telugu',
            'kn': 'Kannada',
            'ta': 'Tamil',
            'en': 'English'
        }

        detected_language_name = language_names.get(detected_language, 'English')

        # Check if query is in Telugu, Kannada, or Tamil
        if detected_language not in ['te', 'kn', 'ta']:
            return jsonify({
                "success": False,
                "error": f"This endpoint is specifically for Telugu, Kannada, and Tamil queries. Detected language: {detected_language_name}",
                "detected_language": detected_language_name,
                "original_query": original_query
            }), 400

        # Special handling for Tamil queries - try to route to Tamil FAISS API first
        if detected_language == 'ta':
            try:
                from services.tamil_query_router import tamil_router

                # Check if we should route to Tamil API
                if tamil_router.should_route_to_tamil_api(original_query):
                    print(f"🌏 Routing Tamil query to Tamil FAISS API...")
                    tamil_response = tamil_router.route_tamil_query(original_query)

                    # Check if Tamil API was successful (no error field means success)
                    if not tamil_response.get('error'):
                        print(f"✅ Tamil FAISS API response received")
                        # Return the Tamil response directly since it's already in Tamil
                        return jsonify(tamil_response)
                    else:
                        print(f"⚠️ Tamil FAISS API failed: {tamil_response.get('error')}, falling back to translation flow")
                else:
                    print(f"🔄 Tamil query will use standard translation flow")

            except ImportError:
                print(f"⚠️ Tamil router not available, using translation flow")
            except Exception as e:
                print(f"⚠️ Tamil routing failed: {e}, using translation flow")

        # Step 1: Translate query to English
        english_query = original_query
        translation_successful = False

        if detected_language in ['te', 'kn', 'ta']:
            print(f"🌐 Translating {detected_language_name} query to English...")
            translation_result = translation_service.translate_text(
                original_query, 
                target_lang='en', 
                source_lang=detected_language
            )
            
            if translation_result and translation_result.get('translated_text'):
                english_query = translation_result['translated_text']
                translation_successful = True
                print(f"✅ Query translated to English: '{english_query[:50]}...'")
            else:
                print("⚠️ Query translation failed, using original query")

        # Step 2: Prepare data for financial_query endpoint
        financial_query_data = {
            "query": english_query,
            "language": "English",  # We're sending English query
            "enable_translation": False,  # We'll handle translation ourselves
            **{k: v for k, v in data.items() if k not in ['query', 'language', 'enable_translation']}
        }

        # Step 3: Call financial_query endpoint internally
        print(f"📊 Sending English query to financial_query endpoint...")
        
        # Create a new request context for the internal call
        with app.test_request_context('/financial_query', 
                                    method='POST', 
                                    json=financial_query_data,
                                    headers={'Content-Type': 'application/json'}):
            try:
                # Call the financial_query function directly
                financial_response = handle_query()
                
                # Extract the response data
                if hasattr(financial_response, 'get_json'):
                    financial_data = financial_response.get_json()
                else:
                    financial_data = financial_response
                    
            except Exception as e:
                print(f"❌ Error calling financial_query: {str(e)}")
                return jsonify({
                    "success": False,
                    "error": f"Failed to process financial query: {str(e)}",
                    "original_query": original_query,
                    "english_query": english_query,
                    "detected_language": detected_language_name
                }), 500

        # Step 4: Translate the response back to original language
        if financial_data and detected_language in ['te', 'kn', 'ta']:
            print(f"🔄 Translating response back to {detected_language_name}...")
            
            # Translate the main AI response
            if 'ai_response' in financial_data and financial_data['ai_response']:
                response_translation = translation_service.translate_text(
                    financial_data['ai_response'],
                    target_lang=detected_language,
                    source_lang='en'
                )
                
                if response_translation and response_translation.get('translated_text'):
                    # Apply text corruption detection and cleaning to translated response
                    translated_text = response_translation['translated_text']
                    is_corrupted, cleaned_text, corruption_details = detect_text_corruption(translated_text)
                    
                    if is_corrupted:
                        print(f"🔧 Detected and cleaned corruption in {detected_language_name} AI response")
                        print(f"   Corruption type: {corruption_details.get('reason', 'unknown')}")
                        print(f"   Confidence: {corruption_details.get('confidence', 0):.3f}")
                        financial_data['ai_response'] = cleaned_text
                        
                        # Add corruption metadata for debugging
                        if 'translation_metadata' not in financial_data:
                            financial_data['translation_metadata'] = {}
                        financial_data['translation_metadata']['ai_response_cleaned'] = True
                        financial_data['translation_metadata']['corruption_details'] = corruption_details
                    else:
                        financial_data['ai_response'] = translated_text
                    
                    print(f"✅ AI response translated to {detected_language_name}")

            # Translate related questions if present
            if 'related_questions' in financial_data and financial_data['related_questions']:
                translated_questions = []
                for question in financial_data['related_questions']:
                    if question:
                        question_translation = translation_service.translate_text(
                            question,
                            target_lang=detected_language,
                            source_lang='en'
                        )
                        if question_translation and question_translation.get('translated_text'):
                            # Apply text corruption detection and cleaning to translated question
                            translated_question = question_translation['translated_text']
                            is_corrupted, cleaned_question, _ = detect_text_corruption(translated_question)
                            
                            if is_corrupted:
                                print(f"🔧 Cleaned corruption in translated question: '{translated_question[:50]}...'")
                                translated_questions.append(cleaned_question)
                            else:
                                translated_questions.append(translated_question)
                        else:
                            translated_questions.append(question)
                    else:
                        translated_questions.append(question)
                
                financial_data['related_questions'] = translated_questions
                print(f"✅ Related questions translated to {detected_language_name}")

            # Translate any other text fields that might be present
            text_fields_to_translate = ['summary', 'context', 'explanation']
            for field in text_fields_to_translate:
                if field in financial_data and financial_data[field]:
                    field_translation = translation_service.translate_text(
                        financial_data[field],
                        target_lang=detected_language,
                        source_lang='en'
                    )
                    if field_translation and field_translation.get('translated_text'):
                        # Apply text corruption detection and cleaning to translated field
                        translated_field = field_translation['translated_text']
                        is_corrupted, cleaned_field, _ = detect_text_corruption(translated_field)
                        
                        if is_corrupted:
                            print(f"🔧 Cleaned corruption in translated {field}: '{translated_field[:50]}...'")
                            financial_data[field] = cleaned_field
                        else:
                            financial_data[field] = translated_field

        # Step 5: Add translation metadata to response
        financial_data.update({
            "translation_metadata": {
                "original_query": original_query,
                "english_query": english_query,
                "detected_language": detected_language_name,
                "query_translation_successful": translation_successful,
                "response_translated": detected_language in ['te', 'kn'],
                "translation_provider": translation_service.translator_available
            }
        })

        # Step 6: Apply final cleaning to the entire response
        financial_data = clean_multilingual_response(financial_data, detected_language)
        
        print(f"🎉 Multilingual financial query completed successfully for {detected_language_name}")
        return jsonify(financial_data)

    except Exception as e:
        print(f"❌ Error in multilingual_financial_query: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}",
            "original_query": original_query if 'original_query' in locals() else None
        }), 500

@app.route('/api/test_related_questions', methods=['POST'])
def test_related_questions():
    """Enhanced test endpoint for related questions generation with context"""
    try:
        data = request.get_json()
        test_query = data.get('query', 'What is the current market trend?')
        test_answer = data.get('answer', 'The market is showing positive growth with increased investor confidence.')
        test_language = data.get('language', 'English')
        
        # Test context data
        test_context = data.get('context', {
            'retrieved_documents': [
                {
                    'rank': 1,
                    'score': '85%',
                    'date': '2024-01-15',
                    'category': 'Market Analysis',
                    'text': 'Recent market indicators show strong performance across major sectors.'
                },
                {
                    'rank': 2,
                    'score': '78%',
                    'date': '2024-01-14',
                    'category': 'Investment Strategy',
                    'text': 'Diversified portfolios are outperforming single-sector investments.'
                }
            ],
            'index_used': 'financial_data',
            'search_engine': 'FAISS',
            'has_uploaded_content': True,
            'upload_sources': ['Market Report 2024', 'Investment Analysis'],
            'query_language': test_language,
            'response_language': test_language,
            'data_language': 'English'
        })

        # Test the enhanced related questions generation
        related_questions = generate_related_questions(test_query, test_answer, test_language, test_context)

        # Test character diversity analysis
        diversity_analysis = calculate_script_aware_char_diversity(test_answer)
        corruption_analysis = detect_text_corruption(test_answer, diversity_analysis)

        return jsonify({
            "success": True,
            "test_data": {
                "query": test_query,
                "answer": test_answer,
                "language": test_language,
                "context_provided": bool(test_context)
            },
            "results": {
                "related_questions": related_questions,
                "questions_count": len(related_questions)
            },
            "character_analysis": {
                "diversity_ratio": diversity_analysis['diversity_ratio'],
                "normalized_diversity": diversity_analysis['normalized_diversity'],
                "script_type": diversity_analysis['script_type'],
                "complexity_score": diversity_analysis['complexity_score'],
                "is_corrupted": corruption_analysis[0],
                "corruption_confidence": corruption_analysis[2]['confidence'] if len(corruption_analysis) > 2 else 0.0
            },
            "api_status": {
                "deepseek_api_configured": bool(DEEPSEEK_API_KEY),
                "api_environment": API_ENVIRONMENT
            },
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "api_key_configured": bool(DEEPSEEK_API_KEY),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/test_character_diversity', methods=['POST'])
def test_character_diversity():
    """Test endpoint for enhanced character diversity analysis"""
    try:
        data = request.get_json()
        test_text = data.get('text', 'This is a sample text for testing character diversity analysis.')
        
        # Perform enhanced character diversity analysis
        diversity_analysis = calculate_script_aware_char_diversity(test_text)
        corruption_analysis = detect_text_corruption(test_text, diversity_analysis)
        
        return jsonify({
            "success": True,
            "input_text": test_text,
            "text_length": len(test_text),
            "analysis": {
                "basic_diversity": diversity_analysis['diversity_ratio'],
                "normalized_diversity": diversity_analysis['normalized_diversity'],
                "script_type": diversity_analysis['script_type'],
                "script_composition": diversity_analysis['script_composition'],
                "complexity_score": diversity_analysis['complexity_score'],
                "character_distribution": {
                    "most_common_chars": diversity_analysis['char_distribution']['most_common'],
                    "entropy": diversity_analysis['char_distribution']['entropy'],
                    "repetition_score": diversity_analysis['char_distribution']['repetition_score']
                }
            },
            "corruption_detection": {
                "is_corrupted": corruption_analysis[0],
                "cleaned_text": corruption_analysis[1] if corruption_analysis[0] else None,
                "corruption_score": corruption_analysis[2]['corruption_score'],
                "confidence": corruption_analysis[2]['confidence'],
                "indicators": corruption_analysis[2]['indicators']
            },
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/debug/user-indexes', methods=['GET'])
def debug_user_indexes():
    """
    Debug endpoint to check what indexes are available for a user.
    Usage: GET /api/debug/user-indexes?client_email=<EMAIL>
    """
    try:
        client_email = request.args.get('client_email')
        if not client_email:
            return jsonify({
                "error": "client_email parameter is required",
                "usage": "GET /api/debug/user-indexes?client_email=<EMAIL>"
            }), 400

        print(f"🔍 DEBUG: Checking indexes for user: {client_email}")

        # Fetch user's indexes from PINE collection
        response = requests.get(
            f"https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&f1_field=client_S&f1_op=eq&f1_value={client_email}",
            headers={
                "Content-Type": "application/json",
                "xxxid": "PINE"
            }
        )

        debug_info = {
            "client_email": client_email,
            "pine_api_status": response.status_code,
            "pine_api_ok": response.ok,
            "user_indexes": [],
            "available_faiss_files": [],
            "pine_raw_data": None
        }

        if response.ok:
            pine_data = response.json()
            debug_info["pine_raw_data"] = pine_data
            
            if pine_data.get("statusCode") == 200 and pine_data.get("source"):
                user_indexes = []
                for item in pine_data["source"]:
                    try:
                        record = json.loads(item)
                        if record.get("index_name"):
                            user_indexes.append({
                                "index_name": record.get("index_name"),
                                "api_key": record.get("api_key", "")[:10] + "..." if record.get("api_key") else None,
                                "embed_model": record.get("embed_model"),
                                "client": record.get("client_S") or record.get("client")
                            })
                    except json.JSONDecodeError as e:
                        print(f"Error parsing PINE record: {e}")
                        continue
                
                debug_info["user_indexes"] = user_indexes
            else:
                debug_info["error"] = "No data found in PINE collection"
        else:
            debug_info["error"] = f"PINE API failed: {response.status_code}"

        # Check available FAISS files
        try:
            faiss_files = []
            for file in os.listdir(FAISS_DATA_DIR):
                if file.endswith('.index'):
                    index_name = file.replace('.index', '')
                    faiss_files.append(index_name)
            debug_info["available_faiss_files"] = faiss_files
        except Exception as e:
            debug_info["faiss_files_error"] = str(e)

        return jsonify(debug_info), 200

    except Exception as e:
        return jsonify({
            "error": f"Debug endpoint error: {str(e)}",
            "client_email": client_email if 'client_email' in locals() else None
        }), 500

if __name__ == '__main__':
    print("Starting Flask server...")
    print(f"FAISS data directory: {FAISS_DATA_DIR}")
    print(f"Available embedding models: {list(EMBEDDING_MODELS.keys())}")
    print(f"Default embedding model: {DEFAULT_EMBED_MODEL}")
    app.run(debug=True, host='0.0.0.0', port=5010)
